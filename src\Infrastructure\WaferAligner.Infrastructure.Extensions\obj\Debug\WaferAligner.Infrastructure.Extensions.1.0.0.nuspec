﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner.Infrastructure.Extensions</id>
    <version>1.0.0</version>
    <authors>WaferAligner Team</authors>
    <description>WaferAligner基础设施扩展方法库，提供Observable扩展和通用工具方法</description>
    <tags>WaferAligner Extensions Observable Infrastructure</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="System.Reactive" version="5.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Reflection" version="4.3.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\WaferAligner-0717-3.8.12-nullimple-75\src\Infrastructure\WaferAligner.Infrastructure.Extensions\bin\Debug\net6.0-windows\WaferAligner.Infrastructure.Extensions.dll" target="lib\net6.0-windows7.0\WaferAligner.Infrastructure.Extensions.dll" />
  </files>
</package>