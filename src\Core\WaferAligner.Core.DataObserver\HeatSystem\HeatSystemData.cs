﻿using System;
using System.Collections.Generic;

namespace JYJ001.App.DataObserver.HeatSystem
{
    public class HeatSystemData : AbstractSystemData
    {
        //实时刷新
        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        //检查
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }
        //
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>
        {
            // ["TempRecipeControl.TopHeaterReciperFinish"] = typeof(bool),
            //["TempRecipeControl.Loop1WorkingSP"] = typeof(UInt16),
            //["TempRecipeControl.TopHeaterTemperature"] = typeof(Int16),
            ["TempRecipeControl.TopPV"] = typeof(Int16),
            ["TempRecipeControl.BottomPV"] = typeof(Int16),
            ["TempRecipeControl.TopTargetTemperature"] = typeof(Int16),
            ["TempRecipeControl.BottomTargetTemperature"] = typeof(Int16),

            ["TempRecipeControl.TopActions"] = typeof(UInt16),//无 DataUpdate
            ["TempRecipeControl.BottomActions"] = typeof(UInt16),//无 DataUpdate

            ["TempRecipeControl.OpenTopHeaterPower"] = typeof(bool),//无 DataUpdate
            ["TempRecipeControl.OpenBottomHeaterPower"] = typeof(bool),//无 DataUpdate


            //["TempRecipeControl.BottomHeaterTemperature"] = typeof(Int16),
            //["TempRecipeControl.BottomHeaterReciperFinish"] = typeof(bool),
            //["TempRecipeControl.Loop2WorkingSP"] = typeof(UInt16),
            //["Chamber_Controller.CoolingPlateAttached"] = typeof(bool),           

            ["TempRecipeControl.CoolingPlateAttached"] = typeof(bool),
            ["TempRecipeControl.TopCoolingPlateAttached"] = typeof(bool),
            ["TempRecipeControl.BottomCoolingPlateAttached"] = typeof(bool),
            ["TempRecipeControl.Cooler"] = typeof(UInt16)

            //["TempRecipeControl.TopV"] = typeof(UInt16),
            //["TempRecipeControl.TopI"] = typeof(UInt16),
            //["TempRecipeControl.TopGi"] = typeof(UInt16),
            //["TempRecipeControl.TopSetPoint"] = typeof(UInt16),
            //["TempRecipeControl.BottomV"] = typeof(UInt16),
            //["TempRecipeControl.BottomI"] = typeof(UInt16),
            //["TempRecipeControl.BottomGi"] = typeof(UInt16),
            //["TempRecipeControl.BottomSetPoint"] = typeof(UInt16),
            //["CoolingStationControl.StationStatus"] = typeof(bool),
        };

        public HeatSystemData()
        {
            DataUpdate = new Dictionary<string, Action<object>>()
            {
                ["TempRecipeControl.TopPV"] = v => { TopPV = Convert.ToSingle(v) / 10; },
                ["TempRecipeControl.BottomPV"] = v => { BottomPV = Convert.ToSingle(v) / 10; },
                ["TempRecipeControl.TopTargetTemperature"] = v => { TopTargetTemperature = Convert.ToSingle(v) / 10; },
                ["TempRecipeControl.BottomTargetTemperature"] = v => { BottomTargetTemperature = Convert.ToSingle(v) / 10; },


                ["TempRecipeControl.CoolingPlateAttached"] = v => { CoolingPlateStatus = (bool)v; },
                ["TempRecipeControl.TopCoolingPlateAttached"] = v => { TopCoolingPlateAttached = (bool)v; },
                ["TempRecipeControl.BottomCoolingPlateAttached"] = v => { BottomCoolingPlateAttached = (bool)v; },



                //// ["GVL.Loop1PV"] = v => { TopPV = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.Loop1WorkingSP"] = v => { TopWorkingSP = Convert.ToSingle(v) / 10; },
                //// ["GVL.Loop2PV"] = v => { BottomPV = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.Loop2WorkingSP"] = v => { BottomWorkingSP = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.TopHeaterReciperFinish"] = v => { TopHeatFunctionExecuteFinished = (bool)v; },
                //["TempRecipeControl.BottomHeaterReciperFinish"] = v => 

                //["TempRecipeControl.TopV"] = v => { TopV = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.TopI"] = v => { TopI = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.TopGi"] = v => { TopGi = Convert.ToSingle(v) / 100; },
                //["TempRecipeControl.TopSetPoint"] = v =>
                //{
                //    TopSetPoint = Convert.ToSingle(v) / 10;
                //},
                //["TempRecipeControl.BottomV"] = v => { BottomV = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.BottomI"] = v => { BottomI = Convert.ToSingle(v) / 10; },
                //["TempRecipeControl.BottomGi"] = v => { BottomGi = Convert.ToSingle(v) / 100; },
                //["TempRecipeControl.BottomSetPoint"] = v => { BottomSetPoint = Convert.ToSingle(v) / 10; },

                //["CoolingStationControl.StationStatus"] = v => { CoolingStationStatus = (bool)v; }
            };

            DataCheck = new Dictionary<string, Func<object>>()
            {
                ["TopPV"] = () => TopPV,
                ["BottomPV"] = () => BottomPV,
                ["TopTargetTemperature"] = () => TopTargetTemperature,
                ["BottomTargetTemperature"] = () => BottomTargetTemperature,
                //["TopWorkingSP"] = () => TopWorkingSP,
                //["BottomWorkingSP"] = () => BottomWorkingSP,
                ["TempRecipeControl.TopCoolingPlateAttached"] = () => TopCoolingPlateAttached,
                ["TempRecipeControl.BottomCoolingPlateAttached"] = () => BottomCoolingPlateAttached,
                ["TempRecipeControl.CoolingPlateAttached"] = () => CoolingPlateStatus,
                //["TopV"] = () => TopV,
                //["TopI"] = () => TopI,
                //["TopGi"] = () => TopGi,
                //["TopSetPoint"] = () => TopSetPoint,
                //["BottomV"] = () => BottomV,
                //["BottomI"] = () => BottomI,
                //["BottomGi"] = () => BottomGi,
                //["BottomSetPoint"] = () => BottomSetPoint,


            };
        }

        public float TopSP { get; set; }
        public float BottomSP { get; set; }

        public float TopPV;
        public float TopWorkingSP;
        public float BottomPV;
        public float BottomWorkingSP;
        public float TopTargetTemperature;
        public float BottomTargetTemperature;


        public float TopV;
        public float TopI;
        public float TopGi;
        public float TopSetPoint;
        public float BottomV;
        public float BottomI;
        public float BottomGi;
        public float BottomSetPoint;


        public bool TopHeatFunctionExecuteFinished = false;
        public bool BottomHeatFunctionExecuteFinished = false;
        public bool TopCoolingPlateAttached = false;
        public bool BottomCoolingPlateAttached = false;
        public bool CoolingPlateStatus = false;
        public bool CoolingStationStatus = false;

        public override void DataInitial()
        {

        }
    }
}
