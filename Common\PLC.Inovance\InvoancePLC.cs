﻿//using Aya.PLC.Base;
//using PLC.Inovance.Client;
//using System.Collections.ObjectModel;

//namespace PLC.Inovance
//{

//    public class InvoancePlcInstance : IPlcInstance
//    {
//        public static object LockObject = new object();
//        private static InvoancePlcClient _client;
//        public EnumErrorCode ErrorCode { get; private set; }
//        public static bool DeviceState;
//        public bool state { get; set; }
//        public InvoancePlcInstance()
//        {
//            _client = new InvoancePlcClient();
//        }

//        public async void Connect(string ip)
//        {
//            try
//            {
//                if (!DeviceState)
//                {
//                    DeviceState = _client.Connect(ip, CancellationToken.None);
//                }

//                if (_client != null)
//                {
//                    Console.WriteLine("connect(ip)!");
//                }
//                else
//                {
//                    Console.WriteLine("fail");
//                }
//            }
//            catch (Exception e)
//            {
//                Console.WriteLine(e.ToString());
//            }
//        }
//        public void Connect(string ip, int nNetId, int nIpPort)
//        {
//            try
//            {
//                lock (LockObject)
//                {
//                    if (_client == null)
//                    {
//                        _client = new InvoancePlcClient();
//                        _client.Connect(ip, CancellationToken.None);
//                    }
//                }
//            }
//            catch (Exception e)
//            {
//                Console.WriteLine(e.Message.ToString());
//            }
//        }
//        public void Connect()
//        {
//            try
//            {
//                lock (LockObject)
//                {
//                    if (_client == null)
//                    {
//                        _client = new InvoancePlcClient();
//                        _client.Connect(Properties.Resources.ip, CancellationToken.None);
//                    }
//                    else
//                    {
//                        _client.Connect(Properties.Resources.ip, CancellationToken.None);
//                    }
//                }
//            }
//            catch (Exception e)
//            {
//                throw;
//            }
//        }
//        private TRESULT CheckConnectStateBeforeOperator<TRESULT, TINPUT>(Func<TINPUT, TRESULT> func, TINPUT input)
//        {
//            //DeviceState = Init_ETH_String(Properties.Resources.ip, int.Parse(Properties.Resources.nNetId), int.Parse(Properties.Resources.nIpPort));
//            if (_client != null)
//            {
//                short[] pValue = new short[2];

//                //var res = _client.ReadDataAsync("value");
//                var res = _client.KeepAliveRequest();//读一次，得到错误码
//                if (res)
//                {
//                    return func(input);
//                }
//                else
//                {
//                    Connect();
//                    return default(TRESULT)!;
//                }
//            }
//            else
//            {
//                Connect();
//                return default(TRESULT)!;
//            }
//        }
//        private async Task<TRESULT> CheckConnectStateBeforOperatorAsync<TRESULT, TINPUT>(Func<TINPUT, Task<TRESULT>> func, TINPUT input)
//        {
//            if (_client != null)
//            {
//                return await func(input);
//            }
//            else
//            {
//                Connect();
//                return default(TRESULT)!;
//            }
//        }
//        public (uint, (string, Type)) RegisterMonitorVariable<T, S>(T variableInfo, S settings)
//        {

//            throw new NotImplementedException();
//        }

//        public IDictionary<uint, (string, Type)> RegisterMonitorVariables<T, S>(T variableInfo, S settings)
//        {

//            return CheckConnectStateBeforeOperator(new Func<Tuple<T, S>, IDictionary<uint, (string, Type)>>(tuple =>
//            {
//                var (varInfo, setting) = (tuple.Item1, tuple.Item2);
//                if (setting == null) return null!;
//                Dictionary<uint, (string, Type)> ret = new Dictionary<uint, (string, Type)>();
//                if (varInfo is IDictionary<string, Type> variableCollection)
//                {
//                    lock (_client)
//                    {
//                        foreach (var item in variableCollection)
//                        {

//                            var code = _client.AddDeviceNotificationEx(item.Key, new NotificationSettings(TransMode.Cyclic, 500, 0), null, item.Value, out var handler);
//                            if (code == EnumErrorCode.None && handler != uint.MaxValue)
//                            {
//                                ret.Add(handler, (item.Key, item.Value));
//                            }
//                        }
//                        return ret;
//                    }
//                }
//                else if (varInfo is KeyValuePair<string, Type> variable)
//                {
//                    lock (_client)
//                    {
//                        var code = _client.AddDeviceNotificationEx(variable.Key, new NotificationSettings(TransMode.Cyclic, 500, 0), null, variable.Value, out var handler);
//                        ret.Add(handler, (variable.Key, variable.Value));
//                        return ret;
//                    }
//                }
//                else
//                {
//                    return null!;
//                }
//            }), new Tuple<T, S>(variableInfo, settings));
//        }
//        public async Task<bool> WriteVariableAsync<WriteInfo>(WriteInfo info, CancellationToken cancle)
//        {
//            return await CheckConnectStateBeforOperatorAsync(new Func<WriteInfo, Task<bool>>(async (WriteInfos) =>
//            {

//                if (WriteInfos is PLCVarWriteInfo Variable)
//                {
//                    _client.WriteData(Variable.Name, Variable.Value);
//                    return true;
//                }
//                else
//                {
//                    return false;
//                }
//            }), info);
//        }

//        public void UnRegisteMonitorVariables<T>(T variableInfo)
//        {

//        }

//        public async Task<bool> WriteVariablesAsync<WriteInfos>(WriteInfos infos, CancellationToken cancel)
//        {
//            return await CheckConnectStateBeforOperatorAsync(new Func<WriteInfos, Task<bool>>(async (WriteInfos) =>
//            {

//                if (WriteInfos is IEnumerable<PLCVarWriteInfo> Variables)
//                {
//                    foreach (var item in Variables)
//                    {
//                        _client.WriteData(item.Name, item.Value);
//                    }
//                    return true;
//                }
//                else
//                {
//                    return false;
//                }
//            }), infos);
//        }

//        public async Task<object> ReadVariableAsync<ReadInfo>(ReadInfo infos, CancellationToken cancel)
//        {
//            return await CheckConnectStateBeforOperatorAsync(new Func<ReadInfo, Task<object>>(async (infos) =>
//            {
//                if (infos is PLCVarReadInfo readinfo)
//                {
//                    var ret = _client.ReadDataAsync(readinfo.Name, readinfo.Type);
//                    return ret;
//                }
//                else
//                {
//                    return null!;
//                }
//            }
//            ), infos);
//        }

//        public void AddNotification<T>(EventHandler<T> function)
//        {
//            if (function is EventHandler<InvoanceVariableChangedEventArgs> adsHandler)
//            {
//                lock (_client)
//                {
//                    _client.HCNotificationChanged += adsHandler;
//                }
//            }
//        }

//        public void RemoveNotification<NT>(EventHandler<NT> function)
//        {
//            if (function is EventHandler<InvoanceVariableChangedEventArgs> adsHandler)
//            {
//                lock (_client)
//                {
//                    _client.HCNotificationChanged -= adsHandler;
//                }
//            }
//        }
//    }

//}