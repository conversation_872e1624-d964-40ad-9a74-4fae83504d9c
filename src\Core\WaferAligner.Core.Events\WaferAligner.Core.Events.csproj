﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>WaferAligner.Core.Events</AssemblyName>
    <RootNamespace>WaferAligner.Core.Events</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Core.Events</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner事件定义，包含系统中所有事件ID和日志事件</Description>
    <PackageTags>WaferAligner;Core;Events;EventIds;Logging</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
  </ItemGroup>

</Project>
