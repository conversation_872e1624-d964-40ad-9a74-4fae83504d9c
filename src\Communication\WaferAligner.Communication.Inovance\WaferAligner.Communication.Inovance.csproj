<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Platforms>AnyCPU;x64</Platforms>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Communication.Inovance</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner汇川PLC通信实现，支持ModbusTCP协议</Description>
    <PackageTags>WaferAligner;Communication;PLC;Inovance;ModbusTCP</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\WaferAligner.Communication.Abstractions\WaferAligner.Communication.Abstractions.csproj" />
    <ProjectReference Include="..\..\Infrastructure\WaferAligner.Infrastructure.Logging\WaferAligner.Infrastructure.Logging.csproj" />
    <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
  </ItemGroup>

  <!-- 复制汇川PLC通信库 -->
  <ItemGroup>
    <None Include="..\..\..\PLC\PLC.Inovance\StandardModbusApi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
