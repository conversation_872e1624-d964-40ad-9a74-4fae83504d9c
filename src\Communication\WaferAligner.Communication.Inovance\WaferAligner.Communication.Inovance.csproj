﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Platforms>AnyCPU;x64</Platforms>
    <AssemblyName>WaferAligner.Communication.Inovance</AssemblyName>
    <RootNamespace>WaferAligner.Communication.Inovance</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Communication.Inovance</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner汇川PLC通信实现，支持ModbusTCP协议</Description>
    <PackageTags>WaferAligner;Communication;PLC;Inovance;ModbusTCP</PackageTags>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
    <ProjectReference Include="..\WaferAligner.Communication.Abstractions\WaferAligner.Communication.Abstractions.csproj" />
    <ProjectReference Include="..\..\Infrastructure\WaferAligner.Infrastructure.Logging\WaferAligner.Infrastructure.Logging.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>
