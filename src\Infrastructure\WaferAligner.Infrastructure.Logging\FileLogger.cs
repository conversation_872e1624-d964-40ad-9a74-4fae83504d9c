using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace WaferAligner.Infrastructure.Logging
{
    /// <summary>
    /// 文件日志记录器
    /// 支持按日志级别分文件记录，自动创建目录结构
    /// </summary>
    public class FileLogger : ILogger
    {
        private readonly string _name;
        private readonly FileLoggerConfiguration _configuration;
        
        // 日志文件路径映射
        private readonly Dictionary<LogLevel, string> _logFilePaths;
        
        // 最低日志级别
        private LogLevel _minimumLogLevel = LogLevel.Information;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">日志记录器名称</param>
        /// <param name="configuration">日志配置</param>
        public FileLogger(string name, FileLoggerConfiguration configuration = null)
        {
            _name = name ?? throw new ArgumentNullException(nameof(name));
            _configuration = configuration ?? new FileLoggerConfiguration();
            
            // 初始化日志文件路径
            _logFilePaths = InitializeLogFilePaths();
            
            // 确保日志目录存在
            EnsureLogDirectoriesExist();
        }

        /// <summary>
        /// 设置最低日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        public void SetMinimumLogLevel(LogLevel level)
        {
            _minimumLogLevel = level;
        }

        /// <summary>
        /// 开始作用域（暂不实现）
        /// </summary>
        public IDisposable BeginScope<TState>(TState state) => default!;

        /// <summary>
        /// 检查是否启用指定日志级别
        /// </summary>
        public bool IsEnabled(LogLevel logLevel) =>
            _logFilePaths.ContainsKey(logLevel) && logLevel >= _minimumLogLevel;

        /// <summary>
        /// 记录日志
        /// </summary>
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (!IsEnabled(logLevel)) return;

            var message = formatter?.Invoke(state, exception) ?? state?.ToString() ?? string.Empty;
            var logEntry = CreateLogEntry(logLevel, eventId, message, exception);
            
            WriteLogToFile(logLevel, logEntry);
        }

        /// <summary>
        /// 初始化日志文件路径
        /// </summary>
        private Dictionary<LogLevel, string> InitializeLogFilePaths()
        {
            var baseLogPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "WaferAligner", "logs");
            
            var today = DateTime.Today.ToString("yyyyMMdd");
            
            return new Dictionary<LogLevel, string>
            {
                [LogLevel.Trace] = Path.Combine(baseLogPath, "Trace", $"Trace_{today}.log"),
                [LogLevel.Debug] = Path.Combine(baseLogPath, "Debug", $"Debug_{today}.log"),
                [LogLevel.Information] = Path.Combine(baseLogPath, $"Log_{today}.log"),
                [LogLevel.Warning] = Path.Combine(baseLogPath, $"Log_{today}.log"),
                [LogLevel.Error] = Path.Combine(baseLogPath, $"Log_{today}.log"),
                [LogLevel.Critical] = Path.Combine(baseLogPath, $"Log_{today}.log")
            };
        }

        /// <summary>
        /// 确保日志目录存在
        /// </summary>
        private void EnsureLogDirectoriesExist()
        {
            var directories = new HashSet<string>();
            
            foreach (var filePath in _logFilePaths.Values)
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory))
                {
                    directories.Add(directory);
                }
            }

            foreach (var directory in directories)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }

        /// <summary>
        /// 创建日志条目
        /// </summary>
        private string CreateLogEntry(LogLevel logLevel, EventId eventId, string message, Exception exception)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logLevelText = logLevel.ToString().ToUpper();
            var eventIdText = eventId.Id != 0 ? $"[{eventId.Id}]" : "";
            
            var logEntry = $"[{timestamp}] [{logLevelText}] {eventIdText} [{_name}] {message}";
            
            if (exception != null)
            {
                logEntry += Environment.NewLine + exception.ToString();
            }
            
            return logEntry + Environment.NewLine;
        }

        /// <summary>
        /// 将日志写入文件
        /// </summary>
        private void WriteLogToFile(LogLevel logLevel, string logEntry)
        {
            if (!_logFilePaths.TryGetValue(logLevel, out var filePath))
                return;

            try
            {
                // 使用文件锁确保线程安全
                lock (_logFilePaths)
                {
                    File.AppendAllText(filePath, logEntry, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                // 避免日志记录失败导致应用程序崩溃
                Console.WriteLine($"Failed to write log: {ex.Message}");
            }
        }
    }
}
