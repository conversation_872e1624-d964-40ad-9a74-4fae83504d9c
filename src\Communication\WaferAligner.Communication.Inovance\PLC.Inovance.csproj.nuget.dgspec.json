{"format": 1, "restore": {"D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Inovance\\PLC.Inovance.csproj": {}}, "projects": {"D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Base\\PLC.Base.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Base\\PLC.Base.csproj", "projectName": "PLC.Base", "projectPath": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Base\\PLC.Base.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Base\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.401\\RuntimeIdentifierGraph.json"}}}, "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Inovance\\PLC.Inovance.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Inovance\\PLC.Inovance.csproj", "projectName": "PLC.Inovance", "projectPath": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Inovance\\PLC.Inovance.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Inovance\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Base\\PLC.Base.csproj": {"projectPath": "D:\\新建文件夹\\WaferAligner\\WaferAligner\\PLC\\PLC.Base\\PLC.Base.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.401\\RuntimeIdentifierGraph.json"}}}}}