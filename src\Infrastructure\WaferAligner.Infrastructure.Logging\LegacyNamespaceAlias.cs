// 向后兼容的命名空间别名
// 允许现有代码继续使用旧的 Aya.Log 命名空间

using System;

// 全局命名空间别名，使旧代码可以继续工作
global using Aya.Log = WaferAligner.Infrastructure.Logging;

namespace Aya.Log
{
    // 这个命名空间现在是 WaferAligner.Infrastructure.Logging 的别名
    // 所有类型都会自动映射到新的命名空间
}

namespace WaferAligner.Infrastructure.Logging
{
    /// <summary>
    /// 向后兼容性说明
    /// 
    /// 此文件提供向后兼容性支持，允许现有代码继续使用 Aya.Log 命名空间。
    /// 
    /// 迁移建议：
    /// 1. 逐步将现有代码中的 "using Aya.Log;" 替换为 "using WaferAligner.Infrastructure.Logging;"
    /// 2. 在完成所有迁移后，可以删除此文件
    /// 3. 新代码应该直接使用 WaferAligner.Infrastructure.Logging 命名空间
    /// 
    /// 重构历史：
    /// - 原命名空间：Aya.Log
    /// - 新命名空间：WaferAligner.Infrastructure.Logging
    /// - 重构日期：2025-07-30
    /// </summary>
    internal static class BackwardCompatibilityInfo
    {
        public const string OriginalNamespace = "Aya.Log";
        public const string NewNamespace = "WaferAligner.Infrastructure.Logging";
        public const string RefactorDate = "2025-07-30";
        
        [Obsolete("请使用 WaferAligner.Infrastructure.Logging 命名空间")]
        public static void ShowMigrationWarning()
        {
            // 此方法用于在编译时显示迁移警告
        }
    }
}
