﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner.Communication.Abstractions</id>
    <version>1.0.0</version>
    <authors>WaferAligner Team</authors>
    <description>WaferAligner通信抽象层，定义PLC和串口通信的基础接口</description>
    <tags>WaferAligner Communication PLC Abstractions Interface</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Beckhoff.TwinCAT.Ads" version="6.0.216" exclude="Build,Analyzers" />
        <dependency id="Beckhoff.TwinCAT.Ads.Reactive" version="6.0.216" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\WaferAligner-0717-3.8.12-nullimple-75\src\Communication\WaferAligner.Communication.Abstractions\bin\Debug\net6.0\WaferAligner.Communication.Abstractions.dll" target="lib\net6.0\WaferAligner.Communication.Abstractions.dll" />
  </files>
</package>