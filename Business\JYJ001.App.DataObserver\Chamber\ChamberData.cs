﻿using JYJ001.App.CustomControl;
using System;
using System.Collections.Generic;

namespace JYJ001.App.DataObserver.Chamber
{
    public class ChamberData : AbstractSystemData
    {
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>
        {
            ////["CoverController.CylinderLocked"] = typeof(bool),
            ////["CoverController.CoverCondition"] = typeof(UInt16),
            ["Chamber_Controller.SlotValveState"] = typeof(bool),
            ///

            ["Chamber_Controller.ClampsRealState"] = typeof(bool),
            ["Chamber_Controller.Clamp1RealState"] = typeof(bool),
            ["Chamber_Controller.Clamp2RealState"] = typeof(bool),
            ["Chamber_Controller.Clamp3RealState"] = typeof(bool),

            ["Chamber_Controller.SpacersRealState"] = typeof(bool),
            ["Chamber_Controller.Spacer1RealState"] = typeof(bool),
            ["Chamber_Controller.Spacer2RealState"] = typeof(bool),
            ["Chamber_Controller.Spacer3RealState"] = typeof(bool),
            ["Chamber_Controller.OutFinished"] = typeof(bool),
            ["Chamber_Controller.ResetFinished"] = typeof(bool),

        };
        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }

        public ChamberData()
        {
            DataUpdate = new Dictionary<string, Action<object>>()
            {
                ////    ["CoverController.CylinderLocked"] = v => { CylinderLocked = (bool)v; },
                ////    ["CoverController.CoverCondition"] = v =>
                ////    {
                ////        CoverState = (UInt16)v switch
                ////        {
                ////            0 => CoverStatus.Open,
                ////            1 => CoverStatus.Close,
                ////            2 => CoverStatus.Hang,
                ////            3 => CoverStatus.None,
                ////            9 => CoverStatus.Error,
                ////        };
                ////    },
                ["Chamber_Controller.SlotValveState"] = v => { SlotValveState = (bool)v; },

                ///
                ["Chamber_Controller.ClampsRealState"] = v => { ClampsRealState = (bool)v; },
                ["Chamber_Controller.Clamp1RealState"] = v => { Clamp1RealState = (bool)v; },
                ["Chamber_Controller.Clamp2RealState"] = v => { Clamp2RealState = (bool)v; },
                ["Chamber_Controller.Clamp3RealState"] = v => { Clamp3RealState = (bool)v; },


                ["Chamber_Controller.SpacersRealState"] = v => { ClampsRealState = (bool)v; },
                ["Chamber_Controller.Spacers1RealState"] = v => { Spacers1RealState = (bool)v; },
                ["Chamber_Controller.Spacers2RealState"] = v => { Spacers2RealState = (bool)v; },
                ["Chamber_Controller.Spacers3RealState"] = v => { Spacers3RealState = (bool)v; },
                ["Chamber_Controller.OutFinished"] = v => { OutFinished = (bool)v; },
                ["Chamber_Controller.ResetFinished"] = v => { ResetFinished = (bool)v; },
                //["Chamber_Controller.CoolingPlateAttached"] = v => { ClampsRealState = (bool)v; },
                //["Chamber_Controller.TopCoolingPlateAttached"] = v => { ClampsRealState = (bool)v; },
                //["Chamber_Controller.BottomCoolingPlateAttached"] = v => { ClampsRealState = (bool)v; },


            };
            DataCheck = new Dictionary<string, Func<object>>
            {
                ////    ["CoverStatus"] = () => CoverState,
                ////    ["TalonSwitchStatus"] = () => TalonSwitchState,
                ///
                ["SlotValveState"] = () => SlotValveState,

                ["ClampsRealState"] = () => ClampsRealState,
                ["Clamp1RealState"] = () => Clamp1RealState,
                ["Clamp2RealState"] = () => Clamp2RealState,
                ["Clamp3RealState"] = () => Clamp3RealState,

                ["SpacersRealState"] = () => SpacersRealState,
                ["Spacers1RealState"] = () => Spacers1RealState,
                ["Spacers2RealState"] = () => Spacers2RealState,
                ["Spacers3RealState"] = () => Spacers3RealState,

                ["OutFinished"] = () => OutFinished,
                ["ResetFinished"] = () => ResetFinished,
            };
        }

        public bool ClampsRealState;
        public bool Clamp1RealState;
        public bool Clamp2RealState;
        public bool Clamp3RealState;

        public bool SpacersRealState;
        public bool Spacers1RealState;
        public bool Spacers2RealState;
        public bool Spacers3RealState;
        public bool OutFinished;
        public bool ResetFinished;


        //public bool CylinderLocked;
        //public CoverStatus CoverState;
        public bool SlotValveState;


        public override void DataInitial() { }
    }
}
