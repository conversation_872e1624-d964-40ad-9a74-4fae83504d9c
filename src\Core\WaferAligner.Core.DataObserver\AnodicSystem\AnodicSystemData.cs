﻿using JYJ001.App.Business.RecipeParameter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JYJ001.App.DataObserver.AnodicSystem
{
    public class AnodicSystemData : AbstractSystemData
    {

        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>()
        {
            //["AnodicController.HV1RealVoltage"] = typeof(UInt16),
            //["AnodicController.HV1RealCurrent"] = typeof(UInt16),
            //["AnodicController.HV1State"] = typeof(bool),
            //["AnodicController.HV1PorityState"] = typeof(bool),
            //["AnodicController.HV1PorityEnable"] = typeof(bool),
            //["AnodicController.HV1PorityCommandRead"] = typeof(bool),
            //["AnodicController.HV1ReleaseState"] = typeof(bool),
            //["AnodicController.HV1AutoReleaseState"] = typeof(bool),
            //["AnodicController.HV1PorityReverseState"] = typeof(bool),
            //["AnodicController.HV1InterLockState"] = typeof(bool),
            //["AnodicController.HVControlState"] = typeof(bool),
            //["AnodicController.HVOverCurrent"] = typeof(bool),
            //["AnodicController.HVOverTemp"] = typeof(bool),
            //["AnodicController.HV1CurrentLimitSet"] = typeof(UInt16),
            //["AnodicController.HV1VoltageSet"] = typeof(UInt16),
            ["AnodicSystemRecipeControl.VReal"] = typeof(float),
            ["AnodicSystemRecipeControl.IReal"] = typeof(float),
            ["AnodicSystemRecipeControl.SetIRealMax"] = typeof(float),
            ["AnodicSystemRecipeControl.SetV"] = typeof(float),
            ["AnodicSystemRecipeControl.SetTMCVS"] = typeof(float),
            ["AnodicSystemRecipeControl.AnodicAction"] = typeof(UInt16),
            ["AnodicSystemRecipeControl.AnodicControlAction"] = typeof(UInt16),


        };
        public AnodicSystemData()
        {
            DataUpdate = new Dictionary<string, Action<object>>()
            {
                ["AnodicSystemRecipeControl.VReal"] = v => RealVoltage = Convert.ToSingle(v),
                ["AnodicSystemRecipeControl.IReal"] = v => RealCurrent = Convert.ToSingle(v),
                ["AnodicSystemRecipeControl.SetIRealMax"] = v => SetRealCurrentMax = Convert.ToSingle(v),
                ["AnodicSystemRecipeControl.SetV"] = v => SetV = Convert.ToSingle(v),
                ["AnodicSystemRecipeControl.SetTMCVS"] = v => SetTMCVS = Convert.ToSingle(v),

                //["AnodicController.HV1RealVoltage"] = v => HV1RealVoltage = (UInt16)v,
                //["AnodicController.HV1RealCurrent"] = v => HV1RealCurrent = Convert.ToSingle(v) / 10,
                //["AnodicController.HV1State"] = v => HV1State = (bool)v,
                //["AnodicController.HV1PorityState"] = v => HV1PorityState = (bool)v switch
                //{
                //    true => AnodicPority.PositivePole,
                //    false => AnodicPority.NegativePole,
                //},
                //["AnodicController.HV1PorityEnable"] = v => HV1PorityEnable = (bool)v,
                //["AnodicController.HV1PorityCommandRead"] = v => HV1PorityCommandRead = (bool)v,
                //["AnodicController.HV1ReleaseState"] = v => HV1ReleaseState = (bool)v,
                //["AnodicController.HV1AutoReleaseState"] = v => HV1AutoReleaseState = (bool)v,
                //["AnodicController.HV1PorityReverseState"] = v => HV1PorityReverseState = (bool)v,
                //["AnodicController.HV1InterLockState"] = v => HV1InterLockState = (bool)v,
                //["AnodicController.HVControlState"] = v => HVControlState = (bool)v,
                //["AnodicController.HVOverCurrent"] = v => HVOverCurrent = (bool)v,
                //["AnodicController.HVOverTemp"] = v => HVOverTemp = (bool)v,
                //["AnodicController.HV1CurrentLimitSet"] = v => HV1CurrentLimitSet = Convert.ToSingle(v) / 10,
                //["AnodicController.HV1VoltageSet"] = v => HV1VoltageSet = Convert.ToUInt16(v)
            };
            DataCheck = new Dictionary<string, Func<object>>()
            {
                ["AnodicSystemRecipeControl.VReal"] = () => RealVoltage,
                ["AnodicSystemRecipeControl.IReal"] = () => RealCurrent,
                ["AnodicSystemRecipeControl.SetIRealMax"] = () => SetRealCurrentMax,
                ["AnodicSystemRecipeControl.SetV"] = () => SetV,
                ["AnodicSystemRecipeControl.SetTMCVS"] = () => SetTMCVS,

                //["AnodicController.HV1State"] = () => HV1State,
                //["AnodicController.HV1PorityState"] = () => HV1PorityState,
                //["AnodicController.HV1PorityEnable"] = () => HV1PorityEnable,
                //["AnodicController.HV1PorityCommandRead"] = () => HV1PorityCommandRead,
                //["AnodicController.HV1ReleaseState"] = () => HV1ReleaseState,
                //["AnodicController.HV1AutoReleaseState"] = () => HV1AutoReleaseState,
                //["AnodicController.HV1PorityReverseState"] = () => HV1PorityReverseState,
                //["AnodicController.HV1InterLockState"] = () => HV1InterLockState,
                //["AnodicController.HVControlState"] = () => HVControlState,
                //["AnodicController.HVOverCurrent"] = () => HVOverCurrent,
                //["AnodicController.HVOverTemp"] = () => HVOverTemp,
                //["AnodicController.HV1CurrentLimitSet"] = () => HV1CurrentLimitSet,
                //["AnodicController.HV1VoltageSet"] = () => HV1VoltageSet,
            };
        }

        public override void DataInitial()
        {

        }

        public float RealVoltage;
        public float RealCurrent;
        public float SetRealCurrentMax;
        public float SetV;
        public float SetTMCVS;

        public bool HV1State;
        public AnodicPority HV1PorityState;
        public bool HV1PorityEnable;
        public bool HV1PorityCommandRead;
        public bool HV1ReleaseState;
        public bool HV1AutoReleaseState;
        public bool HV1PorityReverseState;
        public bool HV1InterLockState;
        public bool HVControlState;
        public bool HVOverCurrent;
        public bool HVOverTemp;
        public float HV1CurrentLimitSet;
        public UInt16 HV1VoltageSet;
    }
}
