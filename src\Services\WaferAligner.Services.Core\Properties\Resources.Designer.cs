﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace JYJ001.App.Service.Common.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("JYJ001.App.Service.Common.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1000.
        /// </summary>
        internal static string CheckTimerInterval {
            get {
                return ResourceManager.GetString("CheckTimerInterval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AutoRecord.
        /// </summary>
        internal static string InfluxDBAutoRecipeMeasurement {
            get {
                return ResourceManager.GetString("InfluxDBAutoRecipeMeasurement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to http://localhost:8086.
        /// </summary>
        internal static string InfluxDBHost {
            get {
                return ResourceManager.GetString("InfluxDBHost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CETC.
        /// </summary>
        internal static string InfluxDBOrg {
            get {
                return ResourceManager.GetString("InfluxDBOrg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DataRecord.
        /// </summary>
        internal static string InfluxDbRecipeRecordBucket {
            get {
                return ResourceManager.GetString("InfluxDbRecipeRecordBucket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TempRecord.
        /// </summary>
        internal static string InfluxDBTempRecipeMeasurement {
            get {
                return ResourceManager.GetString("InfluxDBTempRecipeMeasurement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to rIBVjuI5tyQ4GEdtnfu-TsAqsUm900ichf8Ium0NRfXJMRKodSey8WB33g9vXqywq6t5ra_AsCnHpT6KnGg5uw==.
        /// </summary>
        internal static string InfuxDBToken {
            get {
                return ResourceManager.GetString("InfuxDBToken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .brcp.
        /// </summary>
        internal static string RecipeFileSufFix {
            get {
                return ResourceManager.GetString("RecipeFileSufFix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10.
        /// </summary>
        internal static string RecordIntervalWithSecond {
            get {
                return ResourceManager.GetString("RecordIntervalWithSecond", resourceCulture);
            }
        }
    }
}
