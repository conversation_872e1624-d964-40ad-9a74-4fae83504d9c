using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace JYJ001.App.Service.Common
{
    public interface IBaseRepository<T> where T : class, new()
    {
        ValueTask<EntityEntry<T>> Insert(T entity);

        void Update(T data);
        Task<bool> UpdateAsycn(Expression<Func<T, bool>> whereLambda, T entity);

        bool Delete(T entity);

        Task<bool> IsExist(Expression<Func<T, bool>> whereLambda);

        Task<T> GetEntity(Expression<Func<T, bool>> whereLambda);
        Task<List<T>> Select();

        Task<List<T>> Select(Expression<Func<T, bool>> whereLambda);

        Task<Tuple<List<T>, int>> Select<S>(int pageSize, int pageIndex, Expression<Func<T, bool>> whereLambda, Expression<Func<T, S>> orderByLambda, bool isAsc);
    }

    public interface IUnitOfWork<DC> where DC : DbContext
    {
        DC GetDbContext();
        Task<int> SaveChangeAsync();
    }
}
