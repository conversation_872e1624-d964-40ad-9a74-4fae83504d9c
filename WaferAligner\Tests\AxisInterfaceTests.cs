using System;
using System.Threading;
using System.Threading.Tasks;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using WaferAligner.Interfaces;
using WaferAligner.Models;
using WaferAligner.Services;
using WaferAligner.Services.PlcCommunication;

namespace WaferAligner.Tests
{
    [TestClass]
    public class AxisInterfaceTests
    {
        private Mock<IPlcCommunication> _plcCommunicationMock;
        private Mock<ILoggingService> _loggingServiceMock;
        private Mock<IAxisEventService> _axisEventServiceMock;

        [TestInitialize]
        public void Setup()
        {
            _plcCommunicationMock = new Mock<IPlcCommunication>();
            _loggingServiceMock = new Mock<ILoggingService>();
            _axisEventServiceMock = new Mock<IAxisEventService>();
        }

        [TestMethod]
        public async Task ZAxisViewModel_ImplementsIZAxisViewModel()
        {
            // 创建ZAxisViewModelNew实例
            var zAxisViewModel = new ZAxisViewModelNew(
                _plcCommunicationMock.Object,
                _loggingServiceMock.Object,
                _axisEventServiceMock.Object);

            // 验证它实现了IZAxisViewModel接口
            Assert.IsInstanceOfType(zAxisViewModel, typeof(IZAxisViewModel), "ZAxisViewModelNew应实现IZAxisViewModel接口");
            
            // 验证常见的操作方法存在并且可以调用
            _plcCommunicationMock.Setup(p => p.GetPositionAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1000);
            
            // 测试异步方法
            var position = await zAxisViewModel.GetCurrentPositionAsync(CancellationToken.None);
            Assert.AreEqual(1000, position, "GetCurrentPositionAsync应该返回正确的位置");
            
            // 验证Z轴特有的方法
            _plcCommunicationMock.Setup(p => p.WriteVariableAsync<double>(
                    It.IsAny<string>(), It.IsAny<string>(), It.IsAny<double>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            
            var result = await zAxisViewModel.SetHomeOffsetAsync(10, CancellationToken.None);
            Assert.IsTrue(result, "SetHomeOffsetAsync应该返回true");
            
            // 验证取消操作支持
            using (var cts = new CancellationTokenSource())
            {
                cts.Cancel();
                try
                {
                    await zAxisViewModel.SetHomeOffsetAsync(10, cts.Token);
                    Assert.Fail("SetHomeOffsetAsync应该抛出OperationCanceledException");
                }
                catch (OperationCanceledException)
                {
                    // 正确抛出异常，测试通过
                }
                catch (Exception ex)
                {
                    Assert.Fail($"SetHomeOffsetAsync抛出了错误的异常类型: {ex.GetType().Name}");
                }
            }
        }

        [TestMethod]
        public async Task CameraAxisViewModel_ImplementsICameraAxisViewModel()
        {
            // 创建CameraAxisViewModelNew实例
            var cameraAxisViewModel = new CameraAxisViewModelNew(
                "LX",
                "Left",
                _plcCommunicationMock.Object,
                _loggingServiceMock.Object,
                _axisEventServiceMock.Object);

            // 验证它实现了ICameraAxisViewModel接口
            Assert.IsInstanceOfType(cameraAxisViewModel, typeof(ICameraAxisViewModel), "CameraAxisViewModelNew应实现ICameraAxisViewModel接口");
            Assert.AreEqual("Left", cameraAxisViewModel.CameraPosition, "CameraPosition应该正确设置");
            
            // 验证常见的操作方法存在并且可以调用
            _plcCommunicationMock.Setup(p => p.MoveToPositionAsync(
                    It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            
            // 测试相机轴特有的方法
            var result = await cameraAxisViewModel.MoveToWorkPositionAsync(CancellationToken.None);
            Assert.IsTrue(result, "MoveToWorkPositionAsync应该返回true");
            
            // 验证SetCameraSpeedAsync方法
            _plcCommunicationMock.Setup(p => p.SetRunSpeedAsync(
                    It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
            
            result = await cameraAxisViewModel.SetCameraSpeedAsync(100, CancellationToken.None);
            Assert.IsTrue(result, "SetCameraSpeedAsync应该返回true");
            
            // 验证取消操作支持
            using (var cts = new CancellationTokenSource())
            {
                cts.Cancel();
                try
                {
                    await cameraAxisViewModel.MoveToSafePositionAsync(cts.Token);
                    Assert.Fail("MoveToSafePositionAsync应该抛出OperationCanceledException");
                }
                catch (OperationCanceledException)
                {
                    // 正确抛出异常，测试通过
                }
                catch (Exception ex)
                {
                    Assert.Fail($"MoveToSafePositionAsync抛出了错误的异常类型: {ex.GetType().Name}");
                }
            }
        }
    }
} 