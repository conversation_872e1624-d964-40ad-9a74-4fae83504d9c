﻿using System;
using System.Collections.Generic;
using System.Reactive.Subjects;

namespace WaferAligner.Core.DataObserver
{
    public abstract class AbstractSystemData
    {
        public Dictionary<string, Subject<object>> DataSource { protected set; get; }
        public abstract Dictionary<string, Type> DataDic { get; protected set; }

        public abstract Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public abstract Dictionary<string, Func<object>> DataCheck { get; protected set; }
        public AbstractSystemData()
        {
            DataSource = new Dictionary<string, Subject<object>>();
            foreach (var item in DataDic)
            {
                DataSource.Add(item.Key, new Subject<object>());
            }
        }

        public abstract void DataInitial();
    }
}
