using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using WaferAligner.EventIds;

namespace JYJ001.App.Service.Common
{
#if NET6

#endif
    public class JsonFileConfiguration : IConfig
    {
        private ConcurrentDictionary<string, object> _systemDefaultConfigurationMap = new ConcurrentDictionary<string, object>();
        private ConcurrentDictionary<string, object> _equipmentSettingMap = new ConcurrentDictionary<string, object>();
        static readonly string StaticConfigPath = @$"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Aligner\default_config.json";
        private readonly ILoggingService _loggingService;
        private string TemporaryConfigurationFilePath = "";
        string ConfigurationFilePath = "";
        public event EventHandler ConfigurationChangeEvent = (s, e) => { };
        public event EventHandler ConfigurationReloadEvent = (s, e) => { };

        private void OnConfigurationChanged(object sender, EventArgs e)
        {
            ConfigurationChangeEvent?.Invoke(sender, e);
        }

        //string RecipeConfigFilePath = 
        public JsonFileConfiguration(ILoggingService loggingService)
        {
            _loggingService = loggingService;
            if (!File.Exists(StaticConfigPath))
            {
                Dictionary<string, object> systemDefaultConfiguration = new Dictionary<string, object>()
                {
                    { "CheckTimerInterval",10},
                    { "InfluxDBAutoRecipeMeasurement","AutoRecord" },
                    { "InfluxDBHost","http://localhost:8086"},
                    { "InfluxDBOrg", "CETC" },
                    { "InfluxDbRecipeRecordBucket", "DataRecord" },
                    { "InfluxDBTempRecipeMeasurement", "TempRecord" },
                    { "InfuxDBToken", "RrahC_wVC7fj44k_5zbFgG4NE2MtbMZt03ISnSpNpy0Cbh42SVG1Dv_g6wZYyx31vFpBRRXXZn00kMJNLtMO9g==" },
                    { "RecipeFileSufFix", ".brcp"},
                    { "RecordIntervalWithSecond", 10 },
                    { "PostgresqlConnectString" ,"Host=localhost;Port=5432;Username=postgres;Password=************;database=postgres" },
                    { "equipment_setting_config_path", $@"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Aligner\Config\default.json"},
                };
                File.WriteAllText(StaticConfigPath, JObject.FromObject(systemDefaultConfiguration).ToString());
            }
            if (!Directory.Exists($@"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Aligner\Config"))
            {
                Directory.CreateDirectory($@"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Alignerder\Config");
            }
            if (!File.Exists($@"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Aligner\Config\default.json"))
            {
                using (var fs = File.Create($@"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Aligner\Config\default.json"))
                {
                    fs.Write(new byte[] { 0x7B, 0x7D }, 0, 2);
                }
            }
            var jobj = JObject.Load(new JsonTextReader(File.OpenText(StaticConfigPath)));
            ConfigurationFilePath = jobj.SelectToken("equipment_setting_config_path").ToObject<string>();
            if (string.IsNullOrEmpty(ConfigurationFilePath))
            {
                _loggingService.LogError("�޷��ҵ������ļ�", EventIds.Configuration_Error);
            }
            var ret1 = LoadConfiguration(StaticConfigPath, _systemDefaultConfigurationMap);
            var ret2 = LoadConfiguration(ConfigurationFilePath, _equipmentSettingMap);
            if (!ret1)
            {
                _loggingService.LogError($"����ϵͳͨ�������ļ���������", EventIds.Load_Default_Configuration_Failed);
            }
            else
            {
                if (!ret2)
                {
                    _loggingService.LogError($"�����豸�����ļ���������", EventIds.Load_User_Configuration_Failed);
                    TemporaryConfigurationFilePath = ConfigurationFilePath;
                }
            }
            if (ret1 & ret2) _loggingService.LogInformation($"���������ļ��ɹ�", EventIds.Configuration_Loaded);
        }

        public void AddOrUpdate(string name, object value)
        {
            _equipmentSettingMap.AddOrUpdate(name, value, (name, v1) =>
            {
                return value;
            });
        }

        public object GetValue(string name, string mode = "systerm")
        {
            if (mode == "systerm")
            {
                return _systemDefaultConfigurationMap.TryGetValue(name, out object res) switch
                {
                    true => res,
                    false => null
                };
            }
            else if (mode == "equipment")
            {
                return _equipmentSettingMap.TryGetValue(name, out object res) switch
                {
                    true => res,
                    false => null
                };
            }
            return null;
        }

        public void SaveConfiguration()
        {
            lock (_equipmentSettingMap)
            {
                try
                {
                    if (string.IsNullOrEmpty(TemporaryConfigurationFilePath))
                    {
                        TemporaryConfigurationFilePath = $@"{Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)}\Aligner\Config\default.json";
                    }
                    var configuration = JObject.FromObject(_equipmentSettingMap);
                    File.WriteAllText(TemporaryConfigurationFilePath, configuration.ToString());
                    _loggingService.LogInformation($"�����ļ������� {TemporaryConfigurationFilePath}", EventIds.Configuration_Saved);
                }
                catch (Exception e)
                {
                    _loggingService.LogError("���������ļ�ʱ��������", EventIds.Configuration_Error);
                }

            }
        }

        public void ImplementConfiguration()
        {
            if (!string.IsNullOrEmpty(TemporaryConfigurationFilePath))
            {
                _systemDefaultConfigurationMap["equipment_setting_config_path"] = TemporaryConfigurationFilePath;
            }
            File.WriteAllText(StaticConfigPath, JObject.FromObject(_systemDefaultConfigurationMap).ToString());
            SaveConfiguration();
            OnConfigurationChanged(this, null);


        }

        public async Task<bool> LoadConfigurationAsync(string loadPath, IDictionary<string, object> configmap, Action executor = null)
        {
            using (var fs = File.OpenText(loadPath.ToString()))
            {
                var json = await JObject.LoadAsync(new JsonTextReader(fs));
                var e = json.GetEnumerator();
                while (e.MoveNext())
                {
                    var kv = e.Current;
                    if (!configmap.TryAdd(kv.Key, kv.Value)) return false;
                }
                try
                {
                    executor();
                }
                catch (Exception)
                {
                    throw;
                }
                return true;
            }

        }

        public bool ReLoadConfiguration(string loadPath)
        {
            if (LoadConfiguration(loadPath, _equipmentSettingMap))
            {
                ConfigurationReloadEvent?.Invoke(this, null);
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool LoadConfiguration(string loadPath, IDictionary<string, object> map)
        {
            if (File.Exists(loadPath))
            {
                using (var fs = File.OpenText(loadPath.ToString()))
                {
                    var json = JObject.Load(new JsonTextReader(fs));
                    var e = json.GetEnumerator();
                    while (e.MoveNext())
                    {
                        var kv = e.Current;
                        if (!map.TryAdd(kv.Key, kv.Value.ToObject<object>())) map[kv.Key] = kv.Value.ToObject<object>();
                    }
                    if (loadPath != StaticConfigPath)
                    {
                        TemporaryConfigurationFilePath = loadPath;
                    }
                    return true;
                }
            }
            else
            {
                return false;
            }
        }
    }
}
