﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net6.0-windows</TargetFrameworks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Beckhoff.TwinCAT.Ads" Version="6.0.216" />
    <PackageReference Include="Beckhoff.TwinCAT.Ads.Reactive" Version="6.0.216" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Desktop\Services\Service.Common\JYJ001.App.Service.Common.Extension\JYJ001.App.Service.Common.Extension.csproj" />
    <ProjectReference Include="..\..\Desktop\Services\Service.Common\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
    <ProjectReference Include="..\PLC.Base\PLC.Base.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
