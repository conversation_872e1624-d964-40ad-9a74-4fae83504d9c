﻿using Aya.DataModel;
using JYJ001.App.Business;
using System;
using System.Threading.Tasks;

namespace JYJ001.App.Services.Common.Interfaces
{
    public interface IAccountService
    {
        public delegate void LogEventHandler(object sender, LogEventArgs args);
        event LogEventHandler LogEvent;
        Task<IRequestResult> LogInCheckAsync(string username,string password);
        IRequestResult LogOut();

        void TransUserInfo();
        UserInfo GetCurrentUser();
        void AddOrUpdateUser(User user);
        void RemoveUser(string name);
    }
}
