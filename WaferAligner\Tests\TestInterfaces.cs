using System;
using System.Threading.Tasks;

namespace WaferAligner.Tests.Mocks
{
    // 这些是临时接口，仅用于测试编译通过
    // 在Phase 3中将创建独立测试项目，使用正式接口
    
    public interface IAxisViewModel
    {
        Task<double> GetPosition();
    }
    
    public interface IXyrAxisViewModel : IAxisViewModel
    {
        Task MoveToPositionAsync(double position);
    }
    
    public interface IZAxisViewModel : IAxisViewModel
    {
        Task HomeOffsetAsync(double offset);
    }
    
    public interface ICameraAxisViewModel : IAxisViewModel
    {
    }
    
    public interface IAxisViewModelFactory
    {
        Task<IXyrAxisViewModel> CreateXAxisAsync();
        Task<IXyrAxisViewModel> CreateYAxisAsync();
        Task<IXyrAxisViewModel> CreateRAxisAsync();
        Task<IZAxisViewModel> CreateZAxisAsync();
        Task<ICameraAxisViewModel> CreateLeftXAxisAsync();
        Task<ICameraAxisViewModel> CreateLeftYAxisAsync();
    }
    
    public interface IMainWindowViewModel
    {
        int TopWaferState { get; set; }
        Task TopWaferExecute();
        Task<bool> SystemPareExcuteAsync(double cameraOffset, double bottomPhoto, double TopGap, double BottomGap);
    }
} 