﻿using Aya.PLC.Base;
using JYJ001.App.Services.Common.Extension;
using JYJ001.App.Services.Common.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TwinCAT;
using TwinCAT.Ads;
using TwinCAT.Ads.TypeSystem;
using TwinCAT.TypeSystem;
using TwinCAT.ValueAccess;

namespace PLC.BeckHoff
{
    public class BeckHoffPlcInstance : IPlcInstance
    {
        #region Private static fileds

        private static AdsClient _client;
        public static object LockObject = new object();
        public static ISymbolCollection<ISymbol> symbols;

        public static uint notificationHandle;
        #endregion Private static fileds

        #region Private fileds

        private readonly ILoggingService _loggingService;
        private static StateInfo stateInfo = new StateInfo();

        #endregion Private fileds

        #region ctor

        public BeckHoffPlcInstance(ILoggingService loggingService)
        {
            _loggingService = loggingService;
        }

        private void OnRouterStateChanged(object sender, AmsRouterNotificationEventArgs e)
        {
            _loggingService.LogInformation($"AdsRouter 状态发生改变 -- {Enum.GetName(e.State)}");
        }

        #endregion ctor



        #region implement interface IPlcInstance

        public void Connect()
        {
            try
            {
                lock (LockObject)
                {
                    if (_client is null)
                    {
                        _loggingService.LogTrace("开始初始化倍福PLC链接!");
                        _client = new AdsClient();
                        _client.RouterStateChanged += new EventHandler<AmsRouterNotificationEventArgs>(OnRouterStateChanged);
                        _client.ConnectionStateChanged += _client_ConnectionStateChanged;

                        Connect(Properties.Resources.AmsNetID, int.Parse(Properties.Resources.AmsPort));
                    }
                }


            }
            catch (Exception e)
            {
                _loggingService.LogError($"连接至目标PLC时发生错误 \n\t {e.Message}!", new Microsoft.Extensions.Logging.EventId(30, " Connection Error"));
            }
        }

        private void _client_ConnectionStateChanged(object sender, ConnectionStateChangedEventArgs e)
        {
            _loggingService.LogDebug($"客户端链接状态从 {e.OldState} 转换至 {e.NewState}");
        }

        public async void Connect(string connectPath, int port, int netId = 0)
        {
            try
            {
                _client.Connect(connectPath, port);

                var code = _client.TryReadState(out stateInfo);

                if (code == AdsErrorCode.NoError)
                {
                    _loggingService.LogTrace("尝试获取PLC Symbolic集合");

                    ISymbolLoader symbolLoader = SymbolLoaderFactory.Create(_client, SymbolLoaderSettings.Default);
                    Task.Run(async () =>
                    {
                        var ret = await symbolLoader.GetSymbolsAsync(CancellationToken.None);
                        if (ret.Succeeded)
                        {
                            if (symbols == null)
                            {
                                symbols = ret.Value;
                                _loggingService.LogTrace("获取 Symbolic 成功!");
                            }
                        }
                        else
                        {
                            _loggingService.LogTrace($"获取 Symbolic 失败!");
                        }
                    });

                }
                else
                {
                    var reason = code switch
                    {
                        0 => "",
                        _ => Enum.GetName(code),
                    };
                    _loggingService.LogError($@"无法链接至ADS(PLC)服务端 : {Properties.Resources.AmsNetID}, {Properties.Resources.AmsPort}
    Reason : {reason}", new Microsoft.Extensions.Logging.EventId(30, "PLC Connected Event"));
                }
            }
            catch (Exception e)
            {
                _loggingService.LogError($@"连接至ADS服务端时发生错误 : {Properties.Resources.AmsNetID}, : {Properties.Resources.AmsPort}",
                    new Microsoft.Extensions.Logging.EventId(30, "PLC Connected Event"));
            }
        }

        private TRESULT CheckConnectStateBeforOperator<TRESULT, TINPUT>(Func<TINPUT, TRESULT> func, TINPUT input)
        {
            if (_client.IsConnected)
            {
                var errorCode = _client.TryReadState(out stateInfo);
                if (errorCode == AdsErrorCode.NoError)
                {
                    return func(input);
                }
                else
                {
                    var reason = errorCode switch
                    {
                        0 => "",
                        _ => Enum.GetName(errorCode),
                    };
                    _loggingService.LogError($@"无法链接至ADS(PLC)服务端 : {Properties.Resources.AmsNetID}, {Properties.Resources.AmsPort}
    Reason : {reason}", new Microsoft.Extensions.Logging.EventId(36, "PLC Connected Event"));
                    return default(TRESULT);
                }
            }
            else
            {
                _loggingService.LogError($@"本地ADS端口未开启", new Microsoft.Extensions.Logging.EventId(36, "Local ADS Port"));
                return default(TRESULT);
            }
        }

        private async Task<TRESULT> CheckConnectStateBeforOperatorAsync<TRESULT, TINPUT>(Func<TINPUT, Task<TRESULT>> func, TINPUT input)
        {
            if (_client.IsConnected)
            {
                var errorCode = _client.TryReadState(out stateInfo);
                if (errorCode == AdsErrorCode.NoError)
                {
                    return await func(input);
                }
                else
                {
                    var reason = errorCode switch
                    {
                        0 => "",
                        _ => Enum.GetName(errorCode),
                    };
                    _loggingService.LogDebug($@"无法链接至ADS(PLC)服务端 : {Properties.Resources.AmsNetID}, {Properties.Resources.AmsPort}
    Reason : {reason}", new Microsoft.Extensions.Logging.EventId(30, "PLC Connected Event"));
                    return default(TRESULT);
                }
            }
            else
            {
                _loggingService.LogError($@"本地ADS端口未开启", new Microsoft.Extensions.Logging.EventId(36, "Local ADS Port"));
                return default(TRESULT);
            }
        }

        public void AddNotification<T>(EventHandler<T> handler)
        {
            CheckConnectStateBeforOperator(
                new Func<EventHandler<T>, int>((hdl) =>
            {
                if (hdl is EventHandler<AdsNotificationExEventArgs> adsHandler)
                {
                    _client.AdsNotificationEx += adsHandler;

                    return 1;
                }
                else
                {
                    _loggingService.LogError($"注册监控数据时发生错误 : {nameof(handler)} -- 数据类型错误!",
                        new Microsoft.Extensions.Logging.EventId(31, "PLC Registty Notification Callback Event"));
                    return 0;
                }


            })
                , handler);
        }

        public IDictionary<uint, (string, Type)> RegisterMonitorVariables<T, S>(T variableInfo, S settings)
        {
            return CheckConnectStateBeforOperator(new Func<Tuple<T, S>, IDictionary<uint, (string, Type)>>(tuple =>
             {
                 var (varInfo, setting) = (tuple.Item1, tuple.Item2);
                 if (varInfo == null)
                 {
                     _loggingService.LogError("待监控的数据为空", new Microsoft.Extensions.Logging.EventId(32, "PLC Registry Variable Event"));
                     return null;
                 }

                 Dictionary<uint, (string, Type)> ret = new Dictionary<uint, (string, Type)>();
                 for (global::System.Int32 i = 0; i < 10; i++)
                 {
                     if (symbols is not null)
                     {
                         break;
                     }
                     Thread.Sleep(1000);
                 }
                 if (symbols is null)
                 {
                     throw new ArgumentNullException("PLC Symbols read timeout after 10s");
                 }
                 if (varInfo is IDictionary<string, Type> variableCollection && setting is NotificationSettings settings)
                 {

                     foreach (var item in variableCollection)
                     {
                         var handler = _client.AddDeviceNotificationEx(item.Key, new NotificationSettings(AdsTransMode.Cyclic, 500, 0), null, item.Value);
                         ret.Add(handler, (item.Key, item.Value));
                     }
                     return ret;
                 }
                 else if (varInfo is KeyValuePair<string, Type> variable && setting is NotificationSettings settings1)
                 {
                     var handler = _client.AddDeviceNotificationEx(variable.Key, settings1, null, variable.Value);
                     ret.Add(handler, (variable.Key, variable.Value));
                     return ret;
                 }
                 else
                 {
                     _loggingService.LogError($"注册监控数据存在错误 : {nameof(varInfo)} -- 类型错误!",
                         new Microsoft.Extensions.Logging.EventId(32, "PLC Registry Variable Event"));
                     return null;
                 }
             }), new Tuple<T, S>(variableInfo, settings));
        }

        [Obsolete("A unimplement exception will throw this method. Use IDictionary<uint, (string, Type)> RegisterMonitorVariables<T>(T variableInfo, NotificationSettings settings) instead")]
        public (uint, (string, Type)) RegisterMonitorVariable<T, S>(T variableInfo, S settings)
        {
            throw new NotImplementedException("Use");
        }

        [Obsolete("A unimplement exception will throw this method.")]
        public void UnRegisteMonitorVariables<T>(T variableInfo)
        {
            CheckConnectStateBeforOperator(new Func<T, int>(varInfo =>
            {
                if (varInfo == null) throw new ArgumentNullException($" : {nameof(variableInfo)} is Null!");

                if (varInfo is IDictionary<string, Type> variableCollection)
                {
                    foreach (var item in variableCollection)
                    {
                        // Todo : Delete registered variable
                        _client.DeleteVariableHandle(89999);
                    }
                    return 1;
                }
                else if (varInfo is KeyValuePair<string, Type> variable)
                {
                    _client.DeleteVariableHandle(89999);
                    return 1;
                }
                else
                {
                    _loggingService.LogError($"取消注册监控变量时发生错误 : {nameof(variableInfo)} -- 数据类型错误!",
                        new Microsoft.Extensions.Logging.EventId(33, "PLC UnRegister Variable Event"));
                    return 0;
                }
            }), variableInfo);
        }

        public async Task<bool> WriteVariableAsync<WriteInfo>(WriteInfo info, CancellationToken cancel)
        {
            return await CheckConnectStateBeforOperatorAsync(new Func<WriteInfo, Task<bool>>(async (writeInfo) =>
            {
                if (writeInfo is PLCVarWriteInfo beckhoffVariable)
                {
                    Symbol symbol = (Symbol)symbols[beckhoffVariable.Name];

                    ResultWriteAccess resultWrite = await symbol.WriteValueAsync(beckhoffVariable.Value, cancel);
                    if (resultWrite.Failed)
                    {
#if DEBUG
                        _loggingService.LogDebug($"向PLC写变量时发生错误 : {nameof(info)}!",
                            new Microsoft.Extensions.Logging.EventId(34, "PLC WriteValue Event"));
#endif
                        _loggingService.LogError($"向PLC写变量时发生错误 : {nameof(info)}!",
                            new Microsoft.Extensions.Logging.EventId(34, "PLC WriteValue Event"));
                        return false;
                    }
                    return true;
                }
                else
                {
                    _loggingService.LogError($"向PLC写变量时发生错误 : {nameof(info)} -- 数据类型错误!",
                        new Microsoft.Extensions.Logging.EventId(34, "PLC WriteValue Event"));
                    return false;
                }
            }), info);
        }

        public async Task<object> ReadVariableAsync<ReadInfo>(ReadInfo info, CancellationToken cancel)
        {
            return await CheckConnectStateBeforOperatorAsync(new Func<ReadInfo, Task<object>>(async (info) =>
            {
                if (info is PLCVarReadInfo beckhoffVariable)
                {
                    Symbol symbol = (Symbol)symbols[beckhoffVariable.Name];

                    ResultReadValueAccess resultRead = await symbol.ReadValueAsync(cancel);
                    if (resultRead.Succeeded)
                        return resultRead.Value;
                    else
                        return null;
                }
                else
                {
                    _loggingService.LogError($"从PLC读变量时发生错误 : {nameof(info)} -- 数据类型错误!",
                        new Microsoft.Extensions.Logging.EventId(35, "PLC ReadValue Event"));
                    return null;
                }
            }), info);
        }

        #endregion implement interface IPlcInstance
    }
}