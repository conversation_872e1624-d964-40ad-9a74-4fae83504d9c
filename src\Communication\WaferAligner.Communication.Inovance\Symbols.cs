﻿namespace PLC.Inovance
{
    using System.Text.Json;
    using System.Text.Json.Serialization;

    public class Symbols
    {
        [JsonPropertyName("Symbolconfiguration")]
        public SymbolConfiguration SymbolConfiguration { set; get; }
        public IEnumerable<SymbolNode>? GetSymbols()
        {
            if (SymbolConfiguration is null) yield return null;
            if (SymbolConfiguration.NodeList.IsNullOrEmpty()) yield return null;
            foreach (var item in SymbolConfiguration.NodeList.ApplicationNode.CollectSymbol())
            {
                yield return item;
            }
        }
    }

    public class SymbolConfiguration
    {
        [JsonPropertyName("@xmlns")]
        public string? Xmlns { set; get; }
        public DescriptionHeader Header { set; get; }
        public TypeDefination? TypeList { set; get; }
        public SymbolList? NodeList { set; get; }
    }

    public class SymbolList
    {
        public ApplicationNode? ApplicationNode { set; get; }

        public bool IsNullOrEmpty() => this.ApplicationNode is null || ApplicationNode.IsNullOrEmpty();

    }

    public class ApplicationNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        public List<PouNode>? PouNodeList { set; get; }
        public bool IsNullOrEmpty() => PouNodeList is null || !PouNodeList.Any();

        public IEnumerable<SymbolNode> CollectSymbol()
        {
            if (IsNullOrEmpty()) yield return null;
            foreach (var pou_item in PouNodeList)
            {
                if (!pou_item.IsNullOrEmpty())
                {
                    foreach (var symbol in pou_item.Node)
                    {
                        var full_named_symbol = symbol;
                        full_named_symbol.Name = pou_item.Name + "." + full_named_symbol.Name;
                        yield return full_named_symbol;
                    }
                }
            }
        }
    }

    public class PouNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        public List<SymbolNode>? Node { set; get; }

        public bool IsNullOrEmpty() => Node is null || !Node.Any();
    }

    public class SymbolNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        [JsonPropertyName("@type")]
        [System.Text.Json.Serialization.JsonConverter(typeof(SymbolJsonStringToTypeConverter))]
        public Type? Type { set; get; }

        [JsonPropertyName("@access")]
        public string? Access { set; get; }

        [JsonPropertyName("@directaddress")]
        [JsonConverter(typeof(AddressParser))]
        public int? Address { set; get; }
    }

    public class AddressParser : JsonConverter<int?>
    {
        public override int? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            try
            {
                var addrStr = reader.GetString().Substring(3);
                return Convert.ToInt32(addrStr);
                //switch (reader.GetString().Substring(0, 3))
                //{
                //    case "%MX":
                //        return Convert.ToInt32(Convert.ToInt32(addrStr)*10);
                //    default:
                //        return Convert.ToInt32(addrStr);
                //}

            }
            catch (Exception)
            {

                return null;
            }
        }

        public override void Write(Utf8JsonWriter writer, int? value, JsonSerializerOptions options)
        {
            throw new NotImplementedException();
        }
    }

    public class TypeDefination
    {
        public List<TypeSimple>? TypeSimple { set; get; }
        public List<TypeUserDefined>? TypeUserDef { set; get; }
    }

    public class TypeSimple
    {
        [JsonPropertyName("@name")]
        public string? Name { set; get; }
        [JsonPropertyName("@size")]
        [JsonConverter(typeof(String2IntConverter))]
        public int Size { set; get; }
        [JsonPropertyName("@swapsize")]
        [JsonConverter(typeof(String2IntConverter))]
        public int SwapSize { set; get; }
        [JsonPropertyName("@typeclass")]
        [JsonConverter(typeof(SymbolJsonStringToTypeConverter))]
        public Type? TypeClass { set; get; }

        [JsonPropertyName("@icename")]
        public string IceName { set; get; }
    }

    public class TypeUserDefined
    {
        [JsonPropertyName("@name")]
        public string? Name { set; get; }
        [JsonPropertyName("@size")]
        [JsonConverter(typeof(String2IntConverter))]
        public int? Size { set; get; }
        [JsonPropertyName("@nativesize")]
        [JsonConverter(typeof(String2IntConverter))]
        public int? NativeSize { set; get; }
        [JsonPropertyName("@swapsize")]
        [JsonConverter(typeof(String2IntConverter))]
        public int? SwapSize { set; get; }
        [JsonPropertyName("@typeclass")]
        public string? IceName { set; get; }
        [JsonPropertyName("@basetype")]
        [JsonConverter(typeof(SymbolJsonStringToTypeConverter))]
        public Type? TypeClass { set; get; }
        [JsonPropertyName("@icename")]
        public string? BaseType { set; get; }

        public string? Comment { set; get; }

        public List<UserDefineTypeElement>? UserDefElement { set; get; }
    }

    public class UserDefineTypeElement
    {
        [JsonPropertyName("@type")]
        public string? Type { set; get; }

        [JsonPropertyName("@icename")]
        public string? IceName { set; get; }

        [JsonPropertyName("@enum_value")]
        public string? EnumValue { set; get; }
    }

    public class String2IntConverter : JsonConverter<int>
    {
        public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            try
            {
                return Convert.ToInt32(reader.GetString());
            }
            catch (Exception)
            {
                throw;
            }

        }

        public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString());
        }
    }

    public class SymbolJsonStringToTypeConverter : System.Text.Json.Serialization.JsonConverter<Type>
    {
        public override Type? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var str = reader.GetString()!;
            return str.ToUpper() switch
            {
                "T_BOOL" => typeof(bool),
                "T_BYTE" or "USINT" => typeof(byte),
                "T_SINT" => typeof(sbyte),
                "T_INT" or "WORD" => typeof(Int16),
                "T_UINT" => typeof(UInt16),
                "T_DWORD" or "DINT" => typeof(Int32),
                "T_DUINT" => typeof(UInt32),
                "T_DINT" => typeof(Int32),
                "T_REAL" => typeof(float),
                "T_LREAL" => typeof(double),
                _ => typeof(UInt16)
            };
        }

        public override void Write(Utf8JsonWriter writer, Type value, JsonSerializerOptions options)
        {
            var typeString = value switch
            {
                var t when t == typeof(bool) => "T_BOOL",
                var t when t == typeof(byte) => "T_BYTE",
                var t when t == typeof(sbyte) => "T_SINT",
                var t when t == typeof(Int16) => "T_INT",
                var t when t == typeof(UInt16) => "T_UINT",
                var t when t == typeof(Int32) => "T_DINT",
                var t when t == typeof(UInt32) => "T_DUINT",
                var t when t == typeof(float) => "T_REAL",
                var t when t == typeof(double) => "T_LREAL",
                _ => "T_UINT" // 默认类型
            };
            writer.WriteStringValue(typeString);
        }
    }


    public class DescriptionHeader
    {
        public string Version { set; get; }
        public ProjectInfo ProjectInfo { set; get; }
        public SymbolConfigObject SymbolConfigObject { set; get; }
    }

    public class SymbolConfigObject
    {
        [JsonPropertyName("@version")]
        public string Version { set; get; }
        [JsonPropertyName("@runtimeid")]
        public string RuntimeId { set; get; }
        [JsonPropertyName("@libversion")]
        public string LibVersion { set; get; }
        [JsonPropertyName("@compiler")]
        public string Compiler { set; get; }
        [JsonPropertyName("@lmm")]
        public string Lmm { set; get; }
        [JsonPropertyName("@profile")]
        public string Profile { set; get; }
        [JsonPropertyName("@settings")]
        public string Settings { set; get; }
    }

    public class ProjectInfo
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        [JsonPropertyName("@devicename")]
        public string DeviceName { set; get; }
        [JsonPropertyName("@appname")]
        public string AppName { set; get; }
    }
}
