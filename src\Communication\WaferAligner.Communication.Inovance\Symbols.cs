using System.Text.Json;
using System.Text.Json.Serialization;

namespace WaferAligner.Communication.Inovance
{
    public class Symbols
    {
        [JsonPropertyName("Symbolconfiguration")]
        public SymbolConfiguration SymbolConfiguration { set; get; }
        
        public IEnumerable<SymbolNode?> GetSymbols()
        {
            if (SymbolConfiguration is null) yield return null;
            if (SymbolConfiguration.NodeList.IsNullOrEmpty()) yield return null;
            foreach (var item in SymbolConfiguration.NodeList.ApplicationNode.CollectSymbol())
            {
                yield return item;
            }
        }
    }

    public class SymbolConfiguration
    {
        [JsonPropertyName("@xmlns")]
        public string? Xmlns { set; get; }
        public DescriptionHeader Header { set; get; }
        public TypeDefination? TypeList { set; get; }
        public SymbolList? NodeList { set; get; }
    }

    public class SymbolList
    {
        public ApplicationNode? ApplicationNode { set; get; }

        public bool IsNullOrEmpty() => this.ApplicationNode is null || ApplicationNode.IsNullOrEmpty();
    }

    public class ApplicationNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        public List<PouNode>? PouNodeList { set; get; }
        public bool IsNullOrEmpty() => PouNodeList is null || !PouNodeList.Any();

        public IEnumerable<SymbolNode> CollectSymbol()
        {
            if (IsNullOrEmpty()) yield return null;
            foreach (var pou_item in PouNodeList)
            {
                if (!pou_item.IsNullOrEmpty())
                {
                    foreach (var symbol in pou_item.Node)
                    {
                        var full_named_symbol = symbol;
                        full_named_symbol.Name = pou_item.Name + "." + full_named_symbol.Name;
                        yield return full_named_symbol;
                    }
                }
            }
        }
    }

    public class PouNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        public List<SymbolNode>? Node { set; get; }

        public bool IsNullOrEmpty() => Node is null || !Node.Any();
    }

    public class SymbolNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        [JsonPropertyName("@type")]
        [System.Text.Json.Serialization.JsonConverter(typeof(SymbolJsonStringToTypeConverter))]
        public Type? Type { set; get; }

        [JsonPropertyName("@access")]
        public string? Access { set; get; }

        [JsonPropertyName("@directaddress")]
        [JsonConverter(typeof(AddressParser))]
        public int? Address { set; get; }
    }

    public class TypeDefination
    {
        public List<TypeNode>? DataTypeList { set; get; }
    }

    public class TypeNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        public List<SubItemNode>? SubItemList { set; get; }
    }

    public class SubItemNode
    {
        [JsonPropertyName("@name")]
        public string Name { set; get; }
        [JsonPropertyName("@type")]
        public string Type { set; get; }
    }

    public static class IEnumerableExtensions
    {
        public static bool IsNullOrEmpty<T>(this IEnumerable<T>? source)
        {
            return source == null || !source.Any();
        }
    }

    public class AddressParser : JsonConverter<int?>
    {
        public override int? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            if (string.IsNullOrEmpty(value))
                return null;

            // 解析地址格式，例如 "%MW100" -> 100
            if (value.StartsWith("%MW"))
            {
                var addressStr = value.Substring(3);
                if (int.TryParse(addressStr, out var address))
                {
                    return address;
                }
            }
            else if (value.StartsWith("%QX"))
            {
                // QX地址处理
                var addressStr = value.Substring(3);
                if (double.TryParse(addressStr, out var qxAddress))
                {
                    return (int)(qxAddress * 10); // QX10.1 -> 101
                }
            }

            return null;
        }

        public override void Write(Utf8JsonWriter writer, int? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                writer.WriteStringValue($"%MW{value.Value}");
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }

    public class SymbolJsonStringToTypeConverter : JsonConverter<Type?>
    {
        public override Type? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            return value?.ToLower() switch
            {
                "t_bool" => typeof(bool),
                "t_byte" => typeof(byte),
                "t_sint" => typeof(sbyte),
                "t_int" => typeof(Int16),
                "t_uint" => typeof(UInt16),
                "t_dint" => typeof(Int32),
                "t_duint" => typeof(UInt32),
                "t_lint" => typeof(Int64),
                "t_ulint" => typeof(UInt64),
                "t_real" => typeof(float),
                "t_lreal" => typeof(double),
                _ => typeof(UInt16) // 默认类型
            };
        }

        public override void Write(Utf8JsonWriter writer, Type value, JsonSerializerOptions options)
        {
            var typeString = value switch
            {
                var t when t == typeof(bool) => "T_BOOL",
                var t when t == typeof(byte) => "T_BYTE",
                var t when t == typeof(sbyte) => "T_SINT",
                var t when t == typeof(Int16) => "T_INT",
                var t when t == typeof(UInt16) => "T_UINT",
                var t when t == typeof(Int32) => "T_DINT",
                var t when t == typeof(UInt32) => "T_DUINT",
                var t when t == typeof(Int64) => "T_LINT",
                var t when t == typeof(UInt64) => "T_ULINT",
                var t when t == typeof(float) => "T_REAL",
                var t when t == typeof(double) => "T_LREAL",
                _ => "T_UINT" // 默认类型
            };
            writer.WriteStringValue(typeString);
        }
    }

    public class DescriptionHeader
    {
        public string Version { set; get; }
        public ProjectInfo ProjectInfo { set; get; }
        public SymbolConfigObject SymbolConfigObject { set; get; }
    }

    public class ProjectInfo
    {
        public string Name { set; get; }
        public string Title { set; get; }
        public string Version { set; get; }
        public string Author { set; get; }
        public string Description { set; get; }
    }

    public class SymbolConfigObject
    {
        public string Name { set; get; }
        public string Source { set; get; }
    }
}
