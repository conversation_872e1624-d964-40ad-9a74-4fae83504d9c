//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("WaferAligner Team")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("WaferAligner日志记录基础设施，提供文件日志记录和配置管理功能")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("WaferAligner.Infrastructure.Logging")]
[assembly: System.Reflection.AssemblyTitleAttribute("WaferAligner.Infrastructure.Logging")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Runtime.Versioning.TargetPlatformAttribute("Windows7.0")]
[assembly: System.Runtime.Versioning.SupportedOSPlatformAttribute("Windows7.0")]

// 由 MSBuild WriteCodeFragment 类生成。

