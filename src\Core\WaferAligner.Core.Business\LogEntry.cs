﻿using Microsoft.Extensions.Logging;
using System;

namespace WaferAligner.Core.Business
{
    public struct LogEntry<T>
    {
        public LogLevel LogLevel { set; get; }
        public EventId Id { set; get; }

        public T LogMessage { set; get; }
        
        // 添加Exception属性，用于保存异常信息
        public Exception Exception { set; get; }

        public DateTime TimeStamp { set; get; }

        public override string ToString()
        {
            var time = $"[{TimeStamp.Year.ToString()}-{TimeStamp.Month.ToString()}-{TimeStamp.Day.ToString()}] ";
            var detailTime = $"[{TimeStamp.Hour.ToString()}:{TimeStamp.Minute.ToString()}:{TimeStamp.Second.ToString()}] ";
            var logLevel = $"[{Enum.GetName(LogLevel)}] ";
            var log = $"[{Id.Id}-{Id.Name}]";
            
            var str = time + detailTime + logLevel + log + $"{LogMessage.ToString()}";
            
            // 如果有异常信息，添加到日志字符串中
            if (Exception != null)
            {
                str += $" Exception: {Exception.Message}";
            }
            
            str += "\r\n";
            return str;
        }
    }
}
