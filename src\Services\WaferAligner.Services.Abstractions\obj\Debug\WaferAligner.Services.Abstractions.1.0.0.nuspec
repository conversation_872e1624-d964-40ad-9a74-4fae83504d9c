﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner.Services.Abstractions</id>
    <version>1.0.0</version>
    <authors>WaferAligner Team</authors>
    <description>WaferAligner服务抽象层，定义核心服务接口</description>
    <tags>WaferAligner Services Abstractions Interface</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="JYJ001.App.Business" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="6.0.11" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\WaferAligner-0717-3.8.12-nullimple-75\src\Services\WaferAligner.Services.Abstractions\bin\Debug\net6.0-windows\WaferAligner.Services.Abstractions.dll" target="lib\net6.0-windows7.0\WaferAligner.Services.Abstractions.dll" />
  </files>
</package>