{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"PLC.Inovance/1.0.0": {"dependencies": {"PLC.Base": "1.0.0"}, "runtime": {"PLC.Inovance.dll": {}}}, "Beckhoff.TwinCAT.Ads/6.0.216": {"dependencies": {"Beckhoff.TwinCAT.Ads.Server": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.ComponentModel.Composition": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Beckhoff.TwinCAT.Ads.Abstractions/6.0.216": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Beckhoff.TwinCAT.Ads.Reactive/6.0.216": {"dependencies": {"Beckhoff.TwinCAT.Ads.Abstractions": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.Reactive.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Beckhoff.TwinCAT.Ads.Server/6.0.216": {"dependencies": {"Beckhoff.TwinCAT.Ads.Abstractions": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.Server.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.ComponentModel.Composition/6.0.0": {"runtime": {"lib/net6.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.1"}}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "PLC.Base/1.0.0": {"dependencies": {"Beckhoff.TwinCAT.Ads": "6.0.216", "Beckhoff.TwinCAT.Ads.Reactive": "6.0.216"}, "runtime": {"PLC.Base.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"PLC.Inovance/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Beckhoff.TwinCAT.Ads/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-V+LccEj1k31u8pD6ETxrht/oK2aMhPRJ7SZnNYLkUucd2q3A2D+KcUYWTm2PGCrCkosohnf/VGbGAfl5EudW0g==", "path": "beckhoff.twincat.ads/6.0.216", "hashPath": "beckhoff.twincat.ads.6.0.216.nupkg.sha512"}, "Beckhoff.TwinCAT.Ads.Abstractions/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-16yaujxvLpWsNk+PUny75sUfH4oN8FUvszwGqEZ4BeScVjgGeMjCeOzIHrP8PN+tLzAxfiR5TN/JSKfhNmaICw==", "path": "beckhoff.twincat.ads.abstractions/6.0.216", "hashPath": "beckhoff.twincat.ads.abstractions.6.0.216.nupkg.sha512"}, "Beckhoff.TwinCAT.Ads.Reactive/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-Nf7HUg4+x4B12u2wM0z8jNzR9U9JM0NiiQIu3n/EyU4sUN2pr4J+bPaBKL9JYn4agV4nUT0RIf4jdMXTtYoGVg==", "path": "beckhoff.twincat.ads.reactive/6.0.216", "hashPath": "beckhoff.twincat.ads.reactive.6.0.216.nupkg.sha512"}, "Beckhoff.TwinCAT.Ads.Server/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-ZpNtECkCWfr4CQ7tuo7ps5uw9frs11wD9kzbsu0doHK5B+YrHEUcZh6uKpGAfCZyU2TBv2UlLtCBhQdC8WwD4w==", "path": "beckhoff.twincat.ads.server/6.0.216", "hashPath": "beckhoff.twincat.ads.server.6.0.216.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "System.ComponentModel.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-60Qv+F7oxomOjJeTDA5Z4iCyFbQ0B/2Mi5HT+13pxxq0lVnu2ipbWMzFB+RWKr3wWKA8BSncXr9PH/fECwMX5Q==", "path": "system.componentmodel.composition/6.0.0", "hashPath": "system.componentmodel.composition.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "PLC.Base/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}