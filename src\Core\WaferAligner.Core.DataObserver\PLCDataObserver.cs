﻿using System;

namespace JYJ001.App.DataObserver
{
    public class PLCDataObserver : IObserver<object>
    {
        public IDisposable unsubscriber;
        private Action<object> _onNext;

        public PLCDataObserver()
        {

        }

        public void Subscribe(IObservable<object> provider, Action<object> onNext)
        {
            if (provider != null) provider.Subscribe(this);
            if (onNext != null) _onNext = onNext;
        }
        public void OnCompleted()
        {
            this.unsubscriber.Dispose();
        }

        public void OnError(Exception error)
        {

        }

        public void OnNext(object value)
        {
            _onNext.Invoke(value);
        }
    }
}
