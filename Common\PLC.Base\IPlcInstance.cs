﻿using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using TwinCAT.Ads;

namespace Aya.PLC.Base
{
    public interface IPlcInstance
    {
        void Connect(string connectPath, int port, int netId = 0);
        void Connect();
        void AddNotification<T>(EventHandler<T> handler);
        IDictionary<uint, (string, Type)> RegisterMonitorVariables<T, S>(T variableInfo, S settings);
        (uint, (string, Type)) RegisterMonitorVariable<T, S>(T variableInfo, S settings);

        void UnRegisteMonitorVariables<T>(T variableInfo);
        //异步读数据
        Task<object> ReadVariableAsync<ReadInfo>(ReadInfo info, CancellationToken cancel);
        //异步写数据
        Task<bool> WriteVariableAsync<WriteInfo>(WriteInfo info, CancellationToken cancle);
    }
}
