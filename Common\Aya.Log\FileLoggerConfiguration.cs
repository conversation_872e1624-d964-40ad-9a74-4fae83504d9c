﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aya.Log
{
    public class FileLoggerConfiguration
    {
        public int EventId { get; set; }

        public Dictionary<LogLevel, string> LogLevels { get; set; } = new()
        {
            [LogLevel.Debug] = System.AppDomain.CurrentDomain.BaseDirectory + @"\DebugLog.txt",
            [LogLevel.Information] = System.AppDomain.CurrentDomain.BaseDirectory + @"\Log.txt",
            [LogLevel.Warning] = System.AppDomain.CurrentDomain.BaseDirectory + @"\Log.txt",
            [LogLevel.Error] = System.AppDomain.CurrentDomain.BaseDirectory + @"\Log.txt",
        };
    }
}
