<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net6.0-windows</TargetFrameworks>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Infrastructure.Logging</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner日志记录基础设施，提供文件日志记录和配置管理功能</Description>
    <PackageTags>WaferAligner;Logging;FileLogger;Infrastructure</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
  </ItemGroup>

  <!-- 暂时移除服务依赖，后续重构时添加 -->
  <!--
  <ItemGroup>
    <ProjectReference Include="..\..\Services\WaferAligner.Services.Abstractions\WaferAligner.Services.Abstractions.csproj" />
  </ItemGroup>
  -->
</Project>
