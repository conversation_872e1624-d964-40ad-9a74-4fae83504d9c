﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net6.0-windows</TargetFramework>
		<AssemblyName>WaferAligner.Services.Core</AssemblyName>
		<RootNamespace>WaferAligner.Services.Core</RootNamespace>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<PackageId>WaferAligner.Services.Core</PackageId>
		<PackageVersion>1.0.0</PackageVersion>
		<Authors>WaferAligner Team</Authors>
		<Description>WaferAligner核心服务实现</Description>
		<PackageTags>WaferAligner;Services;Core;Implementation</PackageTags>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Remove="AccountService.cs" />
	  <Compile Remove="UserRepository.cs" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Reactive" Version="5.0.0" />
	</ItemGroup>
	        <ItemGroup>
                <ProjectReference Include="..\..\Infrastructure\WaferAligner.Infrastructure.Extensions\WaferAligner.Infrastructure.Extensions.csproj" />
                <ProjectReference Include="..\..\Infrastructure\WaferAligner.Infrastructure.Logging\WaferAligner.Infrastructure.Logging.csproj" />
                <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
                <ProjectReference Include="..\WaferAligner.Services.Extensions\WaferAligner.Services.Extensions.csproj" />
                <ProjectReference Include="..\WaferAligner.Services.Abstractions\WaferAligner.Services.Abstractions.csproj" />
        </ItemGroup>
	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<None Update="system_configuration.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>