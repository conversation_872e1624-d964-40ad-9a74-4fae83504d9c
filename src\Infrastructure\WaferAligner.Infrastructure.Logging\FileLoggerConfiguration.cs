using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;

namespace WaferAligner.Infrastructure.Logging
{
    /// <summary>
    /// 文件日志记录器配置
    /// 提供灵活的日志配置选项
    /// </summary>
    public class FileLoggerConfiguration
    {
        /// <summary>
        /// 事件ID（用于兼容性）
        /// </summary>
        public int EventId { get; set; }

        /// <summary>
        /// 日志级别与文件路径映射（已弃用，使用动态路径生成）
        /// </summary>
        [Obsolete("使用动态路径生成，此属性仅用于向后兼容")]
        public Dictionary<LogLevel, string> LogLevels { get; set; } = new()
        {
            [LogLevel.Debug] = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DebugLog.txt"),
            [LogLevel.Information] = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Log.txt"),
            [LogLevel.Warning] = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Log.txt"),
            [LogLevel.Error] = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Log.txt"),
        };

        /// <summary>
        /// 全局最低日志级别
        /// </summary>
        public LogLevel GlobalMinimumLevel { get; set; } = LogLevel.Information;
        
        /// <summary>
        /// 按模块设置的日志级别
        /// </summary>
        public Dictionary<string, LogLevel> ModuleLevels { get; set; } = new Dictionary<string, LogLevel>();
        
        /// <summary>
        /// 是否启用结构化日志
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;
        
        /// <summary>
        /// 是否启用性能日志
        /// </summary>
        public bool EnablePerformanceLogging { get; set; } = false;
        
        /// <summary>
        /// 日志文件基础路径
        /// </summary>
        public string LogBasePath { get; set; } = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
            "WaferAligner", "logs");
        
        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 100;
        
        /// <summary>
        /// 保留的日志文件数量
        /// </summary>
        public int RetainedLogFileCount { get; set; } = 10;
        
        /// <summary>
        /// 是否按日期分割日志文件
        /// </summary>
        public bool SplitByDate { get; set; } = true;
        
        /// <summary>
        /// 日志文件名格式
        /// </summary>
        public string LogFileNameFormat { get; set; } = "yyyyMMdd";

        /// <summary>
        /// 创建开发环境配置
        /// </summary>
        public static FileLoggerConfiguration CreateDevelopmentConfiguration()
        {
            return new FileLoggerConfiguration
            {
                GlobalMinimumLevel = LogLevel.Debug,
                EnableStructuredLogging = true,
                EnablePerformanceLogging = true,
                ModuleLevels = new Dictionary<string, LogLevel>
                {
                    { "PLC", LogLevel.Debug },
                    { "Axis", LogLevel.Debug },
                    { "UI", LogLevel.Information },
                    { "Cylinder", LogLevel.Debug },
                    { "User", LogLevel.Information },
                    { "Config", LogLevel.Debug },
                    { "Serial", LogLevel.Debug },
                    { "Communication", LogLevel.Debug }
                }
            };
        }

        /// <summary>
        /// 创建生产环境配置
        /// </summary>
        public static FileLoggerConfiguration CreateProductionConfiguration()
        {
            return new FileLoggerConfiguration
            {
                GlobalMinimumLevel = LogLevel.Information,
                EnableStructuredLogging = false,
                EnablePerformanceLogging = false,
                MaxLogFileSizeMB = 50,
                RetainedLogFileCount = 30,
                ModuleLevels = new Dictionary<string, LogLevel>
                {
                    { "PLC", LogLevel.Warning },
                    { "Axis", LogLevel.Warning },
                    { "UI", LogLevel.Information },
                    { "Cylinder", LogLevel.Warning },
                    { "User", LogLevel.Information },
                    { "Config", LogLevel.Information },
                    { "Serial", LogLevel.Warning },
                    { "Communication", LogLevel.Warning }
                }
            };
        }

        /// <summary>
        /// 获取指定模块的日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>日志级别</returns>
        public LogLevel GetModuleLogLevel(string moduleName)
        {
            return ModuleLevels.TryGetValue(moduleName, out var level) ? level : GlobalMinimumLevel;
        }

        /// <summary>
        /// 设置模块日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="level">日志级别</param>
        public void SetModuleLogLevel(string moduleName, LogLevel level)
        {
            ModuleLevels[moduleName] = level;
        }
    }
}
