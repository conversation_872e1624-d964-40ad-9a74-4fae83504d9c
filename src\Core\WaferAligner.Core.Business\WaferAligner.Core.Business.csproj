﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <AssemblyName>WaferAligner.Core.Business</AssemblyName>
    <RootNamespace>WaferAligner.Core.Business</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Core.Business</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner核心业务逻辑，包含配方管理、用户认证、日志等核心功能</Description>
    <PackageTags>WaferAligner;Core;Business;Recipe;UserAuth;Logging</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="System.Reactive" Version="5.0.0" />
  </ItemGroup>

</Project>
