﻿namespace PLC.Inovance
{
    public class NotificationSettingsPriorityComparer : IComparer<NotificationSettings?>
    {
        public NotificationSettingsPriorityComparer()
        {

        }

        public int Compare(NotificationSettings? x, NotificationSettings? y)
        {
            if (x == null)
            {
                throw new ArgumentNullException("x");
            }
            if (y == null)
            {
                throw new ArgumentNullException("x");
            }
            if (x!.NotificationMode != y!.NotificationMode)
            {
                if (x!.NotificationMode == TransMode.Cyclic)
                {
                    return 1;
                }
                return -1;
            }
            if (x!.CycleTime != y!.CycleTime)
            {
                if (x!.CycleTime < y!.CycleTime)
                {
                    return 1;
                }

                return -1;
            }

            if (x!.MaxDelay != y!.MaxDelay)
            {
                if (x!.MaxDelay < y!.MaxDelay)
                {
                    return 1;
                }

                return -1;
            }
            return 0;
        }
    }

}
