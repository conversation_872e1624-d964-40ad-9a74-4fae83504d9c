// using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;

namespace WaferAligner.Tests
{
    // [TestClass]
    public class BasicTest
    {
        // [TestMethod]
        public void SimpleTest()
        {
            // 一个简单的测试，确认MSTest框架可以工作
            int a = 1;
            int b = 2;
            int sum = a + b;
            
            // Assert.AreEqual(3, sum);
            Console.WriteLine($"Sum is: {sum}");
        }
    }
} 