using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Communication.Abstractions
{
    /// <summary>
    /// PLC实例通用接口
    /// 定义了PLC通信的基础操作，支持连接管理、变量读写和监控
    /// </summary>
    public interface IPlcInstance
    {
        /// <summary>
        /// PLC连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接到PLC
        /// </summary>
        /// <param name="connectPath">连接路径（IP地址或AMS NetID）</param>
        /// <param name="port">端口号</param>
        /// <param name="netId">网络ID（可选，默认为0）</param>
        void Connect(string connectPath, int port, int netId = 0);

        /// <summary>
        /// 使用默认配置连接到PLC
        /// </summary>
        void Connect();

        /// <summary>
        /// 断开PLC连接
        /// </summary>
        void Disconnect();

        /// <summary>
        /// 添加通知事件处理器
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void AddNotification<T>(EventHandler<T> handler);

        /// <summary>
        /// 移除通知事件处理器
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void RemoveNotification<T>(EventHandler<T> handler);

        /// <summary>
        /// 注册多个监控变量
        /// </summary>
        /// <typeparam name="T">变量信息类型</typeparam>
        /// <typeparam name="S">设置类型</typeparam>
        /// <param name="variableInfo">变量信息</param>
        /// <param name="settings">监控设置</param>
        /// <returns>注册结果字典，键为句柄ID，值为变量名和类型</returns>
        IDictionary<uint, (string Name, Type Type)> RegisterMonitorVariables<T, S>(T variableInfo, S settings);

        /// <summary>
        /// 注册单个监控变量
        /// </summary>
        /// <typeparam name="T">变量信息类型</typeparam>
        /// <typeparam name="S">设置类型</typeparam>
        /// <param name="variableInfo">变量信息</param>
        /// <param name="settings">监控设置</param>
        /// <returns>注册结果，包含句柄ID、变量名和类型</returns>
        (uint Handle, string Name, Type Type) RegisterMonitorVariable<T, S>(T variableInfo, S settings);

        /// <summary>
        /// 取消注册监控变量
        /// </summary>
        /// <typeparam name="T">变量信息类型</typeparam>
        /// <param name="variableInfo">变量信息</param>
        void UnRegisterMonitorVariables<T>(T variableInfo);

        /// <summary>
        /// 异步读取变量
        /// </summary>
        /// <typeparam name="TReadInfo">读取信息类型</typeparam>
        /// <param name="info">读取信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>读取的变量值</returns>
        Task<object?> ReadVariableAsync<TReadInfo>(TReadInfo info, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步写入变量
        /// </summary>
        /// <typeparam name="TWriteInfo">写入信息类型</typeparam>
        /// <param name="info">写入信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteVariableAsync<TWriteInfo>(TWriteInfo info, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量读取变量
        /// </summary>
        /// <typeparam name="TReadInfo">读取信息类型</typeparam>
        /// <param name="infos">读取信息列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>读取结果字典，键为变量名，值为变量值</returns>
        Task<IDictionary<string, object?>> ReadVariablesBatchAsync<TReadInfo>(IEnumerable<TReadInfo> infos, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量写入变量
        /// </summary>
        /// <typeparam name="TWriteInfo">写入信息类型</typeparam>
        /// <param name="infos">写入信息列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>写入结果字典，键为变量名，值为是否成功</returns>
        Task<IDictionary<string, bool>> WriteVariablesBatchAsync<TWriteInfo>(IEnumerable<TWriteInfo> infos, CancellationToken cancellationToken = default);
    }
}
