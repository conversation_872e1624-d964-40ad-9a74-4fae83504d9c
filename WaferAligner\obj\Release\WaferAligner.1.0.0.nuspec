﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner</id>
    <version>1.0.0</version>
    <authors>WaferAligner</authors>
    <description>Package Description</description>
    <dependencies>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="PLC.Base" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="PLC.Inovance" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="CommunityToolkit.Mvvm" version="8.2.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net6.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WindowsForms" />
      </group>
    </frameworkReferences>
    <contentFiles>
      <files include="any/net6.0-windows7.0/gerber.ico" buildAction="Content" />
    </contentFiles>
  </metadata>
  <files>
    <file src="D:\对准\WaferAligner\Release\net6.0-windows\WaferAligner.runtimeconfig.json" target="lib\net6.0-windows7.0\WaferAligner.runtimeconfig.json" />
    <file src="D:\对准\WaferAligner\Release\net6.0-windows\WaferAligner.dll" target="lib\net6.0-windows7.0\WaferAligner.dll" />
    <file src="D:\对准\WaferAligner-20240925\WaferAligner\gerber.ico" target="content\gerber.ico" />
    <file src="D:\对准\WaferAligner-20240925\WaferAligner\gerber.ico" target="contentFiles\any\net6.0-windows7.0\gerber.ico" />
  </files>
</package>