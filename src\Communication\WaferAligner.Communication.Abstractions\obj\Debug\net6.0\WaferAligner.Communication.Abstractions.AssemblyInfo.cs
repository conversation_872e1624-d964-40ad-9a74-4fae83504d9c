//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("WaferAligner Team")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("WaferAligner通信抽象层，定义PLC和串口通信的基础接口")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("WaferAligner.Communication.Abstractions")]
[assembly: System.Reflection.AssemblyTitleAttribute("WaferAligner.Communication.Abstractions")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// 由 MSBuild WriteCodeFragment 类生成。

