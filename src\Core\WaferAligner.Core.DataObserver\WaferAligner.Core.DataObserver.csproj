<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0-windows</TargetFramework>
		<AssemblyName>WaferAligner.Core.DataObserver</AssemblyName>
		<RootNamespace>WaferAligner.Core.DataObserver</RootNamespace>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<PackageId>WaferAligner.Core.DataObserver</PackageId>
		<PackageVersion>1.0.0</PackageVersion>
		<Authors>WaferAligner Team</Authors>
		<Description>WaferAligner数据观察者，提供系统数据监控和PLC数据观察功能</Description>
		<PackageTags>WaferAligner;Core;DataObserver;PLC;SystemData</PackageTags>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="System.Reactive" Version="5.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\WaferAligner.Core.Business\WaferAligner.Core.Business.csproj" />
	</ItemGroup>

</Project>
