﻿using WaferAligner.Communication.Abstractions;
using WaferAligner.Communication.Inovance.Client;
using Microsoft.Extensions.Logging;
using WaferAligner.EventIds;

namespace WaferAligner.Communication.Inovance
{

    public class InvoancePlcInstance : IPlcInstance
    {
        // 改进1：统一连接状态管理
        public static object LockObject = new();
        private static InvoancePlcClient _client = new();
        private static volatile bool _isConnected = false;
        private static volatile bool _isConnecting = false;
        private static DateTime _lastConnectionAttempt = DateTime.MinValue;
        
        public EnumErrorCode ErrorCode { get; private set; }
        public static bool DeviceState;
        public bool State { get; set; }
        
        // 统一的连接状态访问接口
        public bool IsConnected => _isConnected;
        public bool IsConnecting => _isConnecting;
        
        // 添加日志服务
        private ILoggingService _loggingService;
        
        public InvoancePlcInstance()
        {
            // 默认构造函数，日志服务将通过SetLoggingService方法设置
            _loggingService = null;
        }
        
        public InvoancePlcInstance(ILoggingService loggingService)
        {
            _loggingService = loggingService;
        }
        
        // 提供设置日志服务的方法，用于依赖注入
        public void SetLoggingService(ILoggingService loggingService)
        {
            _loggingService = loggingService;
        }

        public void Connect(string ip, int nIpPort, int nNetId)
        {
            // 改进4：改进异常处理
            try
            {
                lock (LockObject)
                {
                    if (_isConnected)
                    {
                        _loggingService?.LogDebug("PLC已连接，跳过重复连接请求", EventIds.Plc_Already_Connected);
                        return;
                    }
                    
                    if (_isConnecting)
                    {
                        _loggingService?.LogDebug("PLC正在连接中，跳过重复连接请求", EventIds.Plc_Connecting);
                        return;
                    }
                    
                    _isConnecting = true;
                    _lastConnectionAttempt = DateTime.Now;
                    _loggingService?.LogInformation($"开始连接汇川PLC: {ip}:{nIpPort}", EventIds.Plc_Connection_Started);
                    
                    var success = _client.Connect(ip, nIpPort, CancellationToken.None);
                    _isConnected = success;
                    
                    if (success)
                    {
                        _loggingService?.LogInformation($"汇川PLC连接成功: {ip}:{nIpPort}", EventIds.Plc_Connection_Succeeded);
                    }
                    else
                    {
                        _loggingService?.LogWarning($"汇川PLC连接失败: {ip}:{nIpPort}", EventIds.Plc_Connection_Failed);
                    }
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _loggingService?.LogError($"连接汇川PLC时发生异常: {ip}:{nIpPort}, 错误: {ex.Message}", EventIds.Plc_Connect_Exception);
                // 不重新抛出异常，避免崩溃异步重连任务
            }
            finally
            {
                _isConnecting = false;
            }
        }
        
        public void Connect()
        {
            // 改进4：改进异常处理
            try
            {
                lock (LockObject)
                {
                    if (_isConnected)
                    {
                        _loggingService?.LogDebug("PLC已连接，跳过重复连接请求", EventIds.Plc_Already_Connected);
                        return;
                    }
                    
                    if (_isConnecting)
                    {
                        _loggingService?.LogDebug("PLC正在连接中，跳过重复连接请求", EventIds.Plc_Connecting);
                        return;
                    }
                    
                    // 防止频繁重连，至少间隔5秒
                    if (DateTime.Now - _lastConnectionAttempt < TimeSpan.FromSeconds(5))
                    {
                        _loggingService?.LogDebug("距离上次连接尝试不足5秒，跳过连接请求", EventIds.Plc_Connect_Too_Frequent);
                        return;
                    }
                    
                    _isConnecting = true;
                    _lastConnectionAttempt = DateTime.Now;
                    
                    var ip = Properties.Resources.ip;
                    var port = Convert.ToInt32(Properties.Resources.port);
                    _loggingService?.LogInformation($"开始连接汇川PLC: {ip}:{port}", EventIds.Plc_Connection_Started);
                    
                    if (_client == null)
                    {
                        _client = new InvoancePlcClient();
                        _loggingService?.LogDebug("创建新的PLC客户端实例", EventIds.Plc_Client_Created);
                    }
                    
                    var success = _client.Connect(ip, port, CancellationToken.None);
                    _isConnected = success;
                    
                    if (success)
                    {
                        _loggingService?.LogInformation($"汇川PLC连接成功: {ip}:{port}", EventIds.Plc_Connection_Succeeded);
                    }
                    else
                    {
                        _loggingService?.LogWarning($"汇川PLC连接失败: {ip}:{port}", EventIds.Plc_Connection_Failed);
                    }
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _loggingService?.LogError($"连接汇川PLC时发生异常: {ex.Message}", EventIds.Plc_Connect_Exception);
                // 不重新抛出异常，避免崩溃异步重连任务
            }
            finally
            {
                _isConnecting = false;
            }
        }
        
        // 添加断开连接方法
        public void Disconnect()
        {
            try
            {
                lock (LockObject)
                {
                                    if (_client != null && _isConnected)
                {
                    _loggingService?.LogInformation("正在断开汇川PLC连接", EventIds.Plc_Disconnection_Started);
                    _client.Dispose();
                    _isConnected = false;
                    _loggingService?.LogInformation("汇川PLC连接已断开", EventIds.Plc_Disconnection_Completed);
                }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"断开汇川PLC连接时发生异常: {ex.Message}", EventIds.Plc_Disconnect_Exception);
            }
        }
        
        private TRESULT CheckConnectStateBeforeOperator<TRESULT, TINPUT>(Func<TINPUT, TRESULT> func, TINPUT input)
        {
            try
            {
                // 改进1：使用统一的连接状态管理
                if (_client != null && _isConnected)
                {
                    // 使用KeepAliveRequest检查连接是否仍然有效
                    var res = _client.KeepAliveRequest();
                    if (res)
                    {
                        return func(input);
                    }
                    else
                    {
                        // 连接状态不一致，更新状态并尝试重连
                        _isConnected = false;
                        _loggingService?.LogWarning("检测到PLC连接断开，将尝试重连", EventIds.Plc_Connection_Lost);
                        
                        // 异步尝试重连，不阻塞当前操作
                        Task.Run(() => Connect());
                        return default(TRESULT)!;
                    }
                }
                else if (!_isConnected && !_isConnecting)
                {
                    // 未连接且未在连接中，异步尝试连接
                    _loggingService?.LogDebug("PLC未连接，将尝试连接", EventIds.Plc_Not_Connected);
                    Task.Run(() => Connect());
                    return default(TRESULT)!;
                }
                else
                {
                    // 正在连接中或客户端为空
                    _loggingService?.LogDebug("PLC正在连接中或客户端不可用", EventIds.Plc_Unavailable);
                    return default(TRESULT)!;
                }
            }
            catch (Exception ex)
            {
                // 改进4：改进异常处理
                _isConnected = false;
                _loggingService?.LogError($"检查PLC连接状态时发生异常: {ex.Message}", EventIds.Plc_State_Check_Exception);
                
                // 异步尝试重连，使用安全的异常处理
                Task.Run(() =>
                {
                    try 
                    { 
                        Connect(); 
                    } 
                    catch (Exception reconnectEx)
                    {
                        _loggingService?.LogError($"重连PLC时发生异常: {reconnectEx.Message}", EventIds.Plc_Reconnect_Exception);
                    }
                });
                return default(TRESULT)!;
            }
        }
        private async Task<TRESULT> CheckConnectStateBeforOperatorAsync<TRESULT, TINPUT>(Func<TINPUT, Task<TRESULT>> func, TINPUT input)
        {
            try
            {
                // 改进1：使用统一的连接状态管理
                if (_client != null && _isConnected)
                {
                    // 使用KeepAliveRequest检查连接是否仍然有效
                    var res = _client.KeepAliveRequest();
                    if (res)
                    {
                        return await func(input);
                    }
                    else
                    {
                        // 连接状态不一致，更新状态并尝试重连
                        _isConnected = false;
                        _loggingService?.LogWarning("检测到PLC连接断开，将尝试重连", EventIds.Plc_Connection_Lost_Async);
                        
                        // 异步尝试重连，不阻塞当前操作
                        _ = Task.Run(() => Connect());
                        return default!;
                    }
                }
                else if (!_isConnected && !_isConnecting)
                {
                    // 未连接且未在连接中，异步尝试连接
                    _loggingService?.LogDebug("PLC未连接，将尝试连接", EventIds.Plc_Not_Connected_Async);
                    _ = Task.Run(() => Connect());
                    return default!;
                }
                else
                {
                    // 正在连接中或客户端为空
                    _loggingService?.LogDebug("PLC正在连接中或客户端不可用", EventIds.Plc_Unavailable_Async);
                    return default!;
                }
            }
            catch (Exception ex)
            {
                // 改进4：改进异常处理
                _isConnected = false;
                _loggingService?.LogError($"检查PLC连接状态时发生异常: {ex.Message}", EventIds.Plc_State_Check_Exception_Async);
                
                // 异步尝试重连，使用安全的异常处理
                _ = Task.Run(() =>
                {
                    try 
                    { 
                        Connect(); 
                    } 
                    catch (Exception reconnectEx)
                    {
                        _loggingService?.LogError($"重连PLC时发生异常: {reconnectEx.Message}", EventIds.Plc_Reconnect_Exception_Async);
                    }
                });
                return default!;
            }
        }
        public (uint, (string, Type)) RegisterMonitorVariable<T, S>(T variableInfo, S settings)
        {
            return CheckConnectStateBeforeOperator(new Func<Tuple<T, S>, (uint, (string, Type))>(tuple =>
            {
                var (varInfo, setting) = (tuple.Item1, tuple.Item2);
                if (setting == null) return (0u, (string.Empty, typeof(object)));
                
                if (varInfo is KeyValuePair<string, Type> variable)
                {
                    lock (_client)
                    {
                        var code = _client.AddDeviceNotificationEx(variable.Key, new NotificationSettings(TransMode.Cyclic, 500, 0), null, variable.Value, out var handler);
                        if (code == EnumErrorCode.None && handler != uint.MaxValue)
                        {
                            return (handler, (variable.Key, variable.Value));
                        }
                        else
                        {
                            _loggingService?.LogWarning($"注册变量监控失败: {variable.Key}, 错误码: {code}", WaferAligner.EventIds.EventIds.Plc_Variables_Registry_Failed);
                            return (0u, (variable.Key, variable.Value));
                        }
                    }
                }
                else
                {
                    _loggingService?.LogWarning($"不支持的变量信息类型: {varInfo?.GetType().Name}", WaferAligner.EventIds.EventIds.Plc_Variables_Registry_Failed);
                    return (0u, (string.Empty, typeof(object)));
                }
            }), new Tuple<T, S>(variableInfo, settings));
        }

        public IDictionary<uint, (string, Type)> RegisterMonitorVariables<T, S>(T variableInfo, S settings)
        {
            //private TRESULT CheckConnectStateBeforeOperator<TRESULT, TINPUT>(Func<TINPUT, TRESULT> func, TINPUT input)
            return CheckConnectStateBeforeOperator(new Func<Tuple<T, S>, IDictionary<uint, (string, Type)>>(tuple =>
            {
                var (varInfo, setting) = (tuple.Item1, tuple.Item2);
                if (setting == null) return null!;
                Dictionary<uint, (string, Type)> ret = new();
                if (varInfo is IDictionary<string, Type> variableCollection)
                {
                    lock (_client)
                    {
                        foreach (var item in variableCollection)
                        {

                            var code = _client.AddDeviceNotificationEx(item.Key, new NotificationSettings(TransMode.Cyclic, 500, 0), null, item.Value, out var handler);
                            if (code == EnumErrorCode.None && handler != uint.MaxValue)
                            {
                                ret.Add(handler, (item.Key, item.Value));
                            }
                        }
                        return ret;
                    }
                }
                else if (varInfo is KeyValuePair<string, Type> variable)
                {
                    lock (_client)
                    {
                        var code = _client.AddDeviceNotificationEx(variable.Key, new NotificationSettings(TransMode.Cyclic, 500, 0), null, variable.Value, out var handler);
                        ret.Add(handler, (variable.Key, variable.Value));
                        return ret;
                    }
                }
                else
                {
                    return null!;
                }
            }), new Tuple<T, S>(variableInfo, settings));
        }
        public async Task<bool> WriteVariableAsync<WriteInfo>(WriteInfo info, CancellationToken cancle)
        {
            return await CheckConnectStateBeforOperatorAsync(new Func<WriteInfo, Task<bool>>(async (WriteInfos) =>
            {

                if (WriteInfos is PLCVarWriteInfo Variable)
                {
                    var write_size = _client.WriteData(Variable.Name, Variable.Value);
                    return write_size > 0;
                }
                else
                {
                    return false;
                }
            }), info);
        }

        public void UnRegisteMonitorVariables<T>(T variableInfo)
        {

        }

        public async Task<bool> WriteVariablesAsync<WriteInfos>(WriteInfos infos, CancellationToken cancel)
        {
            return await CheckConnectStateBeforOperatorAsync(new Func<WriteInfos, Task<bool>>(async (WriteInfos) =>
            {

                if (WriteInfos is IEnumerable<PLCVarWriteInfo> Variables)
                {
                    foreach (var item in Variables)
                    {
                        _client.WriteData(item.Name, item.Value);
                    }
                    return true;
                }
                else
                {
                    return false;
                }
            }), infos);
        }

        public async Task<object> ReadVariableAsync<ReadInfo>(ReadInfo infos, CancellationToken cancel)
        {
            return await CheckConnectStateBeforOperatorAsync(new Func<ReadInfo, Task<object>>(async (infos) =>
            {
                if (infos is PLCVarReadInfo readinfo)
                {
                    var ret = _client.ReadDataAsync(readinfo.Name, readinfo.Type);
                    return ret;
                }
                else
                {
                    return null!;
                }
            }
            ), infos);
        }


        public void AddNotification<T>(EventHandler<T> function)
        {
            if (function is EventHandler<InvoanceVariableChangedEventArgs> adsHandler)
            {
                lock (_client)
                {
                    _client.HCNotificationChanged += adsHandler;
                }
            }
        }

        public void RemoveNotification<NT>(EventHandler<NT> function)
        {
            if (function is EventHandler<InvoanceVariableChangedEventArgs> adsHandler)
            {
                lock (_client)
                {
                    _client.HCNotificationChanged -= adsHandler;
                }
            }
        }
    }
}