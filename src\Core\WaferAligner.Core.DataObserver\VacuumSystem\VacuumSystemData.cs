﻿using System;
using System.Collections.Generic;

namespace JYJ001.App.DataObserver.VacuumSystem
{
    public class VacuumSystemData : AbstractSystemData
    {
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>
        {

            ["Vacuum_System_Controller.ChamberPressure"] = typeof(float),
            ["Vacuum_System_Controller.ForeStageVacuum"] = typeof(float),
            ["Vacuum_System_Controller.MolecularPumpFreq"] = typeof(UInt16),//分子泵频率为什么只是在这个里面有？
            ["Vacuum_System_Controller.WaterPressure"] = typeof(float),

            ["Vacuum_System_Controller.SlowRoughStatus"] = typeof(bool),
            ["Vacuum_System_Controller.FastRoughStatus"] = typeof(bool),
            ["Vacuum_System_Controller.DryPumpStatus"] = typeof(bool),
            ["Vacuum_System_Controller.ForeStageStatus"] = typeof(bool),
            ["Vacuum_System_Controller.TMolecularPumpStatus"] = typeof(bool),
            ["Vacuum_System_Controller.HighValveStatus"] = typeof(bool),

            ["Vacuum_System_Controller.ReleaseValveStatus"] = typeof(bool),
            ["Vacuum_System_Controller.ArValveStatus"] = typeof(bool),
            ["Vacuum_System_Controller.NValveStatus"] = typeof(bool),
            ["Vacuum_System_Controller.HValveStatus"] = typeof(bool),

            ["Vacuum_System_Controller.ChamberVacuum"] = typeof(float),


            ["GVL.DO5EntityMap.bDO.LampOn"] = typeof(bool),
            //["Vacuum_System_Controller.Error"] = typeof(bool),
            //["Vacuum_System_Controller.ErrorCode"] = typeof(byte),



        };
        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }

        public VacuumSystemData()
        {
            DataUpdate = new Dictionary<string, Action<object>>
            {
                ["Vacuum_System_Controller.ChamberPressure"] = (v) =>
                {
                    ChamberPressure = (float)v;
                },
                ["Vacuum_System_Controller.ForeStageVacuum"] = (v) =>
                {
                    ForeStageVacuum = (float)v;
                },
                ["Vacuum_System_Controller.WaterPressure"] = v => { WaterPressure = (float)v; },

                ["Vacuum_System_Controller.SlowRoughStatus"] = (v) => RoughValueStatus = (bool)v,
                ["Vacuum_System_Controller.FastRoughStatus"] = (variable) => FastValveStatus = (bool)variable,

                ["Vacuum_System_Controller.DryPumpStatus"] = (variable) => DryPumpStatus = (bool)variable,
                ["Vacuum_System_Controller.ForeStageStatus"] = (variable) => ForeStageValveStatus = (bool)variable,
                ["Vacuum_System_Controller.TMolecularPumpStatus"] = (variable) => TurboPumpStatus = (bool)variable,
                ["Vacuum_System_Controller.HighValveStatus"] = (variable) => HighValveStatus = (bool)variable,

                ["Vacuum_System_Controller.ReleaseValveStatus"] = (variable) => ReleaseValveStatus = (bool)variable,
                ["Vacuum_System_Controller.ArValveStatus"] = (variable) => PurgeArValveStatus = (bool)variable,
                ["Vacuum_System_Controller.NValveStatus"] = (variable) => PurgeNValveStatus = (bool)variable,
                ["Vacuum_System_Controller.HValveStatus"] = (variable) => PurgeHValveStatus = (bool)variable,

                ["Vacuum_System_Controller.ChamberVacuum"] = (v) => { ChamberVacuum = (float)v; },

                ["GVL.DO5EntityMap.bDO.LampOn"] = (v) => { LampOn = (bool)v; },
            };
            DataCheck = new Dictionary<string, Func<object>>
            {
                ["ChamberPressure"] = () => ChamberPressure,
                ["ForeStageVacumm"] = () => ForeStageVacuum,
                ["WaterPressure"] = () => WaterPressure,

                ["SlowRoughStatus"] = () => RoughValueStatus,
                ["FastRoughStatus"] = () => FastValveStatus,

                ["DryPumpStatus"] = () => DryPumpStatus,
                ["ForeStageValveStatus"] = () => ForeStageValveStatus,
                ["TurboPumpStatus"] = () => TurboPumpStatus,
                ["HighValveStatus"] = () => HighValveStatus,

                ["ReleaseValveStatus"] = () => ReleaseValveStatus,
                ["ArValveStatus"] = () => PurgeArValveStatus,
                ["NValveStatus"] = () => PurgeNValveStatus,
                ["HValveStatus"] = () => PurgeHValveStatus,

                ["ChamberVacuum"] = () => ChamberVacuum,
            };
        }


        private float ChamberPressure;
        private float ForeStageVacuum;
        private float WaterPressure;

        private bool DryPumpStatus;
        private bool RoughValueStatus;
        private bool ForeStageValveStatus;
        private bool HighValveStatus;
        private bool PurgeArValveStatus;
        private bool ReleaseValveStatus;
        private bool FastValveStatus;
        private bool PurgeHValveStatus;
        private bool TurboPumpStatus;
        private bool PurgeNValveStatus;


        private float ChamberVacuum;
        private bool LampOn;
        public override void DataInitial()
        {

        }
    }
}
