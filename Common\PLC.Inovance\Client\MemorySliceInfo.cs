﻿namespace PLC.Inovance.Client
{
    public class MemorySliceInfo
    {
        public int StartAddr;
        public int Length;
        public int Base;
        public MemorySliceInfo(uint handler, int addr, int size, int _base)
        {
            StartAddr = addr;
            Length = size;
            this.Base = _base;
            this.Handlers.Add(handler);
        }
        public HashSet<uint> Handlers = new HashSet<uint>();

        public int ByteNumber() => Length * 2;
        public (bool, string) Insert(uint handler, int startAddr, int offset)
        {
            if (this.Handlers.Add(handler))
            {
                if (this.StartAddr > startAddr)
                {
                    if (this.Length + this.StartAddr < startAddr + offset)
                    {
                        Length = offset;
                    }
                    else if (this.Length + this.StartAddr > startAddr + offset)
                    {
                        Length = this.StartAddr - startAddr + Length;
                    }
                    this.StartAddr = startAddr;

                    return (true, string.Empty);
                }
                else if (this.StartAddr < startAddr)
                {
                    if (this.Length + this.StartAddr < startAddr + offset)
                    {
                        Length = startAddr - this.StartAddr + offset;
                    }

                    return (true, string.Empty);
                }
                //if(this.StartAddr == startAddr) 
                else
                {
                    if (this.Length == offset)
                    {
                        return (false, "Same variable in this slice");
                    }
                    else if (this.Length > offset)
                    {

                        return (true, string.Empty);
                    }
                    else
                    {
                        this.Length = offset;
                        return (true, string.Empty);
                    }
                }
            }
            else
            {
                return (false, "Same variable in this slice");
            }
        }

        public (bool, string) Remove(uint handler, int startAddr, int offset)
        {
            if (this.Handlers.Remove(handler))
            {
                return (true, string.Empty);
            }
            else
            {
                return (false, $"this is no variable with handler {handler}");
            }
        }

        /// <summary>
        /// 1. HandlerList 2.AddrLength 3.StartAddr
        /// </summary>
        /// <returns></returns>
        public (List<uint>, int, int) SnapShot()
        {
            return (this.Handlers.ToList(), this.Length, this.StartAddr);
        }
    }
}
