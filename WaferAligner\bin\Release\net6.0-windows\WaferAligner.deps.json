{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"WaferAligner/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.2.2", "PLC.Base": "1.0.0", "PLC.Inovance": "1.0.0", "Microsoft.Extensions.Configuration": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Logging": "*******", "MySql.Data": "********", "SunnyUI.Common": "*******", "SunnyUI": "*******", "System.Management": "*******", "Ubiety.Dns.Core": "*******", "ZstdNet": "*******"}, "runtime": {"WaferAligner.dll": {}}}, "CommunityToolkit.Mvvm/8.2.2": {"runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PLC.Base/1.0.0": {"runtime": {"PLC.Base.dll": {}}}, "PLC.Inovance/1.0.0": {"dependencies": {"PLC.Base": "1.0.0"}, "runtime": {"PLC.Inovance.dll": {}}}, "Microsoft.Extensions.Configuration/*******": {"runtime": {"Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting/*******": {"runtime": {"Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/*******": {"runtime": {"Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "MySql.Data/********": {"runtime": {"MySql.Data.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SunnyUI.Common/*******": {"runtime": {"SunnyUI.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SunnyUI/*******": {"runtime": {"SunnyUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Management/*******": {"runtime": {"System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.3761.0"}}}, "Ubiety.Dns.Core/*******": {"runtime": {"Ubiety.Dns.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ZstdNet/*******": {"runtime": {"ZstdNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.Configuration.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/*******": {"runtime": {"Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/*******": {"runtime": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/*******": {"runtime": {"Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/*******": {"runtime": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"runtime": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"runtime": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/*******": {"runtime": {"Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/*******": {"runtime": {"Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"runtime": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"runtime": {"Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/*******": {"runtime": {"Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventLog/*******": {"runtime": {"Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Configuration/*******": {"runtime": {"Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Console/*******": {"runtime": {"Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Debug/*******": {"runtime": {"Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventSource/*******": {"runtime": {"Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Configuration.Install/*******": {"runtime": {"System.Configuration.Install.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.3761.0"}}}, "Microsoft.JScript/10.0.0.0": {"runtime": {"Microsoft.JScript.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "14.8.3761.0"}}}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"runtime": {"Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/*******": {"runtime": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}}}, "libraries": {"WaferAligner/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "path": "communitytoolkit.mvvm/8.2.2", "hashPath": "communitytoolkit.mvvm.8.2.2.nupkg.sha512"}, "PLC.Base/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PLC.Inovance/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "MySql.Data/********": {"type": "reference", "serviceable": false, "sha512": ""}, "SunnyUI.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SunnyUI/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Management/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Ubiety.Dns.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "ZstdNet/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Configuration.Install/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.JScript/10.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}