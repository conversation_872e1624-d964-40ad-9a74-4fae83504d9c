{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\JYJ001.App.Service.Extension\\JYJ001.App.Service.Extension.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj", "projectName": "JYJ001.App.Business", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj", "projectName": "JYJ001.App.DataObserver", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Extension\\Aya.Extension.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Extension\\Aya.Extension.csproj", "projectName": "Aya.Extension", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Extension\\Aya.Extension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Extension\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Reactive": {"target": "Package", "version": "[5.0.0, )"}, "System.Reflection": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Log\\Aya.Log.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Log\\Aya.Log.csproj", "projectName": "Aya.Log", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Log\\Aya.Log.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Log\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj", "projectName": "Craft.Base", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Executer\\Craft.Executer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Executer\\Craft.Executer.csproj", "projectName": "Craft.Executer", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Executer\\Craft.Executer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Executer\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj", "projectName": "PLC.Base", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Beckhoff.TwinCAT.Ads": {"target": "Package", "version": "[6.0.216, )"}, "Beckhoff.TwinCAT.Ads.Reactive": {"target": "Package", "version": "[6.0.216, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Inovance\\PLC.Inovance.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Inovance\\PLC.Inovance.csproj", "projectName": "PLC.Inovance", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Inovance\\PLC.Inovance.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Inovance\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj", "projectName": "JYJ001.App.DataModel", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj", "projectName": "JYJ001.APP.ORM", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.11, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[6.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj", "projectName": "JYJ001.App.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj", "projectName": "JYJ001.App.CustomControl", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\JYJ001.App.Service.Extension\\JYJ001.App.Service.Extension.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\JYJ001.App.Service.Extension\\JYJ001.App.Service.Extension.csproj", "projectName": "JYJ001.App.Service.Extension", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\JYJ001.App.Service.Extension\\JYJ001.App.Service.Extension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\JYJ001.App.Service.Extension\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe\\JYJ001.App.Service.Recipe.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe\\JYJ001.App.Service.Recipe.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record\\JYJ001.App.Service.Record.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record\\JYJ001.App.Service.Record.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Service.SubSystem\\JYJ001.App.Service.SubSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Service.SubSystem\\JYJ001.App.Service.SubSystem.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Prism.Core": {"target": "Package", "version": "[9.0.537, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj", "projectName": "JYJ001.App.Service.Common.Extension", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj", "projectName": "JYJ001.App.Service.Common.Interface", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.App.DataModel\\JYJ001.App.DataModel.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj", "projectName": "JYJ001.App.Service.Common", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Extension\\Aya.Extension.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Extension\\Aya.Extension.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Log\\Aya.Log.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Aya.Log\\Aya.Log.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\DataModel\\JYJ001.APP.ORM\\JYJ001.APP.ORM.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj", "projectName": "JYJ001.App.Service.Recipe.Interface", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe\\JYJ001.App.Service.Recipe.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe\\JYJ001.App.Service.Recipe.csproj", "projectName": "JYJ001.App.Service.Recipe", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe\\JYJ001.App.Service.Recipe.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Executer\\Craft.Executer.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Executer\\Craft.Executer.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.Core\\JYJ001.App.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"SunnyUI": {"target": "Package", "version": "[*******, )"}, "SunnyUI.Common": {"target": "Package", "version": "[3.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj", "projectName": "JYJ001.App.Service.Record.Interface", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0-windows", "net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0-windows7.0": {"targetAlias": "net5.0-windows", "projectReferences": {}}, "net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net5.0-windows7.0": {"targetAlias": "net5.0-windows", "dependencies": {"InfluxDB.Client": {"target": "Package", "version": "[4.7.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[5.0.0, 5.0.0]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[5.0.0, 5.0.0]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[5.0.0, 5.0.0]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}, "net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"InfluxDB.Client": {"target": "Package", "version": "[4.7.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record\\JYJ001.App.Service.Record.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record\\JYJ001.App.Service.Record.csproj", "projectName": "JYJ001.App.Service.Record", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record\\JYJ001.App.Service.Record.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Record\\JYJ001.App.Service.Record.Interface\\JYJ001.App.Service.Record.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"InfluxDB.Client": {"target": "Package", "version": "[4.7.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Service.SubSystem\\JYJ001.App.Service.SubSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Service.SubSystem\\JYJ001.App.Service.SubSystem.csproj", "projectName": "JYJ001.App.Service.SubSystem", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Service.SubSystem\\JYJ001.App.Service.SubSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Service.SubSystem\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.DataObserver\\JYJ001.App.DataObserver.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Inovance\\PLC.Inovance.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Inovance\\PLC.Inovance.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\JYJ001.App.CustomControl\\JYJ001.App.CustomControl.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Extension\\JYJ001.App.Service.Common.Extension.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.Recipe\\JYJ001.App.Service.Recipe.Interface\\JYJ001.App.Service.Recipe.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj", "projectName": "JYJ001.App.Serviec.SubSystem.Interface", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\JYJ001.App.Serviec.SubSystem.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Desktop\\Services\\Service.SubSystem\\JYJ001.App.Serviec.SubSystem.Interface\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\Craft.Base\\Craft.Base.csproj"}, "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Bonding - 20250304\\source\\Common\\PLC.Base\\PLC.Base.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}