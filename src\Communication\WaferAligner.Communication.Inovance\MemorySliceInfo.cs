namespace WaferAligner.Communication.Inovance
{
    /// <summary>
    /// 内存切片信息类
    /// 用于管理PLC内存读取的优化切片
    /// </summary>
    public class MemorySliceInfo
    {
        #region 属性

        /// <summary>
        /// 起始地址
        /// </summary>
        public int StartAddr { get; private set; }

        /// <summary>
        /// 长度（字数）
        /// </summary>
        public int Length { get; private set; }

        /// <summary>
        /// 基址
        /// </summary>
        public int Base { get; private set; }

        /// <summary>
        /// 处理器句柄集合
        /// </summary>
        public HashSet<uint> Handlers { get; private set; } = new HashSet<uint>();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="handler">处理器句柄</param>
        /// <param name="addr">地址</param>
        /// <param name="size">大小</param>
        /// <param name="baseAddr">基址</param>
        public MemorySliceInfo(uint handler, int addr, int size, int baseAddr)
        {
            StartAddr = addr;
            Length = size;
            Base = baseAddr;
            Handlers.Add(handler);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取字节数量
        /// </summary>
        /// <returns>字节数量</returns>
        public int ByteNumber() => Length * 2;

        /// <summary>
        /// 插入新的处理器
        /// </summary>
        /// <param name="handler">处理器句柄</param>
        /// <param name="startAddr">起始地址</param>
        /// <param name="offset">偏移量</param>
        /// <returns>插入结果和消息</returns>
        public (bool Success, string Message) Insert(uint handler, int startAddr, int offset)
        {
            if (this.Handlers.Add(handler))
            {
                if (this.StartAddr > startAddr)
                {
                    // 新地址在当前起始地址之前
                    if (this.Length + this.StartAddr < startAddr + offset)
                    {
                        Length = offset;
                    }
                    else if (this.Length + this.StartAddr > startAddr + offset)
                    {
                        Length = this.StartAddr - startAddr + Length;
                    }
                    this.StartAddr = startAddr;

                    return (true, string.Empty);
                }
                else if (this.StartAddr < startAddr)
                {
                    // 新地址在当前起始地址之后
                    if (this.Length + this.StartAddr < startAddr + offset)
                    {
                        Length = startAddr - this.StartAddr + offset;
                    }

                    return (true, string.Empty);
                }
                else
                {
                    // 地址相同
                    if (this.Length == offset)
                    {
                        return (false, "Same variable in this slice");
                    }
                    else if (this.Length > offset)
                    {
                        return (true, string.Empty);
                    }
                    else
                    {
                        this.Length = offset;
                        return (true, string.Empty);
                    }
                }
            }
            else
            {
                return (false, "Same variable in this slice");
            }
        }

        /// <summary>
        /// 移除处理器
        /// </summary>
        /// <param name="handler">处理器句柄</param>
        /// <param name="startAddr">起始地址</param>
        /// <param name="offset">偏移量</param>
        /// <returns>移除结果和消息</returns>
        public (bool Success, string Message) Remove(uint handler, int startAddr, int offset)
        {
            if (this.Handlers.Remove(handler))
            {
                return (true, string.Empty);
            }
            else
            {
                return (false, $"No variable with handler {handler}");
            }
        }

        /// <summary>
        /// 获取快照
        /// </summary>
        /// <returns>处理器列表、地址长度、起始地址</returns>
        public (List<uint> HandlerList, int AddrLength, int StartAddr) SnapShot()
        {
            return (this.Handlers.ToList(), this.Length, this.StartAddr);
        }

        /// <summary>
        /// 检查是否包含指定的处理器
        /// </summary>
        /// <param name="handler">处理器句柄</param>
        /// <returns>是否包含</returns>
        public bool ContainsHandler(uint handler)
        {
            return Handlers.Contains(handler);
        }

        /// <summary>
        /// 获取处理器数量
        /// </summary>
        /// <returns>处理器数量</returns>
        public int HandlerCount => Handlers.Count;

        /// <summary>
        /// 检查地址范围是否重叠
        /// </summary>
        /// <param name="startAddr">起始地址</param>
        /// <param name="length">长度</param>
        /// <returns>是否重叠</returns>
        public bool IsOverlapping(int startAddr, int length)
        {
            int thisEnd = this.StartAddr + this.Length;
            int otherEnd = startAddr + length;

            return !(thisEnd <= startAddr || this.StartAddr >= otherEnd);
        }

        #endregion

        #region 重写方法

        public override string ToString()
        {
            return $"MemorySlice: Addr={StartAddr}, Length={Length}, Handlers={Handlers.Count}";
        }

        public override bool Equals(object? obj)
        {
            if (obj is MemorySliceInfo other)
            {
                return StartAddr == other.StartAddr && Length == other.Length;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(StartAddr, Length);
        }

        #endregion
    }
}
