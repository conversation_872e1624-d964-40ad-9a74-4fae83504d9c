<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net6.0-windows</TargetFrameworks>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Infrastructure.Extensions</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner基础设施扩展方法库，提供Observable扩展和通用工具方法</Description>
    <PackageTags>WaferAligner;Extensions;Observable;Infrastructure</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Reactive" Version="5.0.0" />
    <PackageReference Include="System.Reflection" Version="4.3.0" />
  </ItemGroup>
</Project>
