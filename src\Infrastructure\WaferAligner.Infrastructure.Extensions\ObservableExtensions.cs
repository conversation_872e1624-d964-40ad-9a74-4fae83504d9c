using System;
using System.Threading.Channels;

namespace WaferAligner.Infrastructure.Extensions
{
    /// <summary>
    /// Observable扩展方法
    /// 提供Observable到ChannelReader的转换功能
    /// </summary>
    public static class ObservableExtensions
    {
        /// <summary>
        /// 将Observable转换为ChannelReader
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="observable">Observable源</param>
        /// <param name="maxBufferSize">最大缓冲区大小，null表示无限制</param>
        /// <returns>ChannelReader实例</returns>
        /// <remarks>
        /// 此方法将Observable适配为ChannelReader，不支持背压控制。
        /// 如果连接速度慢于生产者，内存使用量会增加。
        /// 
        /// 对于有界通道，TryWrite返回false时会丢弃数据项。
        /// 另一种选择是使用有界通道，当达到限制时在WaitToWriteAsync上阻塞，
        /// 但这会阻塞线程池线程，不推荐使用。
        /// </remarks>
        public static ChannelReader<T> AsChannelReader<T>(this System.IObservable<T> observable, int? maxBufferSize = null)
        {
            var channel = maxBufferSize != null 
                ? Channel.CreateBounded<T>(maxBufferSize.Value) 
                : Channel.CreateUnbounded<T>();

            var disposable = observable.Subscribe(
                value => channel.Writer.TryWrite(value),
                error => channel.Writer.TryComplete(error),
                () => channel.Writer.TryComplete());

            // 当reader完成时完成订阅
            channel.Reader.Completion.ContinueWith(task => disposable.Dispose());

            return channel.Reader;
        }
    }
}
