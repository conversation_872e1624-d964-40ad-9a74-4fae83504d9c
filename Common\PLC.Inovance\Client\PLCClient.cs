﻿using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;

namespace PLC.Inovance.Client
{

    public class InvoancePlcClient : IDisposable
    {
        #region //标准库
        [DllImport("StandardModbusApi.dll", EntryPoint = "Init_ETH_String", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Init_ETH_String(string sIpAddr, int nNetId = 0, int IpPort = 502);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Exit_ETH", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Exit_ETH(int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        /******************************************************************************
         1.功能描述 : 写Am600软元件
         2.返 回 值 :1 成功  0 失败
         3.参    数 : nNetId:网络链接编号
                      eType：软元件类型    ELEM_QX = 0//QX元件  ELEM_MW = 1 //MW元件
                      nStartAddr:软元件起始地址（QX元件由于带小数点，地址需要乘以10去掉小数点，如QX10.1，请输入101，MW元件直接就是它的元件地址不用处理）
                      nCount：软元件个数
                      pValue：数据缓存区
        4.注意事项 :  1.x和y元件地址需为8进制; 
                      2. 当元件位C元件双字寄存器时，每个寄存器需占4个字节的数据
                      3.如果是写位元件，每个位元件的值存储在一个字节中
        ******************************************************************************/
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);
        /******************************************************************************
         1.功能描述 : 读Am600软元件
         2.返 回 值 :1 成功  0 失败
         3.参    数 : nNetId:网络链接编号
                      eType：软元件类型   ELEM_QX = 0//QX元件  ELEM_MW = 1 //MW元件
                      nStartAddr:软元件起始地址（QX元件由于带小数点，地址需要乘以10去掉小数点，如QX10.1，请输入101，其它元件不用处理）
                      nCount：软元件个数
                      pValue：返回数据缓存区
         4.注意事项 : 如果是读位元件，每个位元件的值存储在一个字节中，pValue数据缓存区字节数必须是8的整数倍
        ******************************************************************************/
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);
        #endregion

        static readonly List<VariableSymbol> Symbols = new List<VariableSymbol>();
        readonly ReadPLCMemorySlices slices = new();
        CancellationTokenSource cts = new CancellationTokenSource();
        private bool connected = false;
        private object LockObj = new();
        private SemaphoreSlim se = new SemaphoreSlim(0);
        
        // 移除BackgroundWorker，使用Task-based异步模式
        // BackgroundWorker queryWorker = new();
        private Task _queryTask = null;
        private CancellationTokenSource _queryTaskCts = new CancellationTokenSource();
        
        // 添加日志服务成员变量
        private readonly ILoggingService _logger;
        
        //private Channel<object> cahnnel = new Channel<object>();
        public InvoancePlcClient(ILoggingService logger = null)
        {
            _logger = logger;
            this.LoadSymbols();
            
            // 移除事件注册和启动
            // queryWorker.DoWork += DoWork;
            // queryWorker.RunWorkerAsync();
            
            // 替换为Task-based异步模式
            _queryTaskCts = new CancellationTokenSource();
            _queryTask = Task.Run(async () => await DoWorkAsync(_queryTaskCts.Token), _queryTaskCts.Token);
            _logger?.LogInformation("已启动PLC查询任务", WaferAligner.EventIds.EventIds.PlcConnectSuccess);
        }

        // 替换原DoWork事件处理程序
        // private async void DoWork(object? sender, DoWorkEventArgs e)
        private async Task DoWorkAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger?.LogInformation("PLC查询任务已启动", WaferAligner.EventIds.EventIds.PlcConnectSuccess);
                
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        var slices_len = slices.Buffer.Count;
                        if (slices_len > 0)
                        {
                            for (int i = 0; i < slices_len && !cancellationToken.IsCancellationRequested; i++)
                            {
                                var info = slices.Buffer[i].SnapShot();
                                var number = info.Item2;
                                byte[] read_buffer = new byte[number * 2];
                                //byte[] read_buffer = new byte[128];
                                Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, info.Item3, info.Item2, read_buffer);

                                foreach (var handler in info.Item1)
                                {
                                    try
                                    {

                                        var symbol = Symbols.Find(v => v.Handler == handler && v.RegistryNumber > 0);
                                        var baseaddr = (symbol.Address - info.Item3) * 2;
                                        symbol.SetValue(new Span<byte>(read_buffer, baseaddr, symbol.Size));
                                        //var v = CastValue(symbol.GetValuePointer(), symbol.DataType);
                                        var v = symbol.GetValue();
                                        this.HCNotificationChanged.Invoke(this, new InvoanceVariableChangedEventArgs(symbol.Name, handler, v));
                                    }
                                    catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
                                    {
                                        _logger?.LogError(ex, $"处理PLC变量时发生异常: {symbol?.Name}", WaferAligner.EventIds.EventIds.PlcVariableReadError);
                                        // 增加取消检查
                                        throw new Exception(ex.ToString());
                                    }
                                }
                            }
                        }

                        // 使用支持取消的延迟
                        try
                        {
                            await Task.Delay(100, cancellationToken);
                        }
                        catch (OperationCanceledException)
                        {
                            // 任务取消时正常结束，不记录异常
                            _logger?.LogDebug("PLC查询任务延迟被取消，准备退出循环", WaferAligner.EventIds.EventIds.ResourceReleased);
                            break;
                        }
                    }
                    catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
                    {
                        // 记录异常但继续运行
                        _logger?.LogError(ex, $"PLC查询循环中发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.PlcVariableReadError);
                        
                        // 短暂延迟避免频繁记录相同错误
                        try
                        {
                            await Task.Delay(1000, cancellationToken);
                        }
                        catch (OperationCanceledException)
                        {
                            // 任务取消时正常结束，不记录异常
                            _logger?.LogDebug("错误延迟被取消，准备退出循环", WaferAligner.EventIds.EventIds.ResourceReleased);
                            break;
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 任务取消是正常流程，记录信息级别日志
                _logger?.LogInformation("PLC查询任务已正常取消", WaferAligner.EventIds.EventIds.ResourceReleased);
            }
            catch (Exception ex)
            {
                // 只有非取消异常才记录为错误
                if (!(ex is OperationCanceledException))
                {
                    _logger?.LogError(ex, $"PLC查询任务异常终止: {ex.Message}", WaferAligner.EventIds.EventIds.PlcConnectFailed);
                }
            }
            finally
            {
                _logger?.LogDebug("PLC查询任务已结束", WaferAligner.EventIds.EventIds.PlcDisconnectComplete);
            }
        }

        public bool Connect(string ipAddress, CancellationToken? token)
        {
            lock (LockObj)
            {
                if (!connected)
                {
                    if (Init_ETH_String(ipAddress, 0, 502))
                    {
                        connected = true;
                        //thread.Start(this.cts.Token);
                        _logger?.LogInformation($"已连接到PLC: {ipAddress}:502", WaferAligner.EventIds.EventIds.PlcConnectSuccess);
                        return true;
                    }
                    else
                    {
                        _logger?.LogError($"连接PLC失败: {ipAddress}:502", WaferAligner.EventIds.EventIds.PlcConnectFailed);
                        return false;
                    }
                }
                else
                {
                    return true;
                }
            }

        }

        object CastValue(Span<byte> buffers, Type type)
        {
            switch (type.Name.ToLower())
            {
                case "boolean":
                    return BitConverter.ToBoolean(buffers);
                case "byte":
                    return buffers[0];
                case "sbyte":
                    return buffers[0];
                case "uint16":
                    return BitConverter.ToUInt16(buffers);
                case "int16":
                    return BitConverter.ToInt16(buffers);
                case "int32":
                    return BitConverter.ToInt32(buffers);
                case "uint32":
                    return BitConverter.ToUInt32(buffers);
                case "single":
                    return BitConverter.ToSingle(buffers);
                case "double":
                    return BitConverter.ToDouble(buffers);
                case "int64":
                    return BitConverter.ToInt64(buffers);
                case "uint64":
                    return BitConverter.ToUInt64(buffers);
                default:
                    return null;
            };
        }
        private bool LoadException = false;
        private string ExceptionString = "";
        IEnumerable<VariableSymbol> ParseFromJson()
        {
            var symbols = new Inovance.Symbols();
            var dir_path = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Bonder\SymbolDescription\";
            var symbolFilePath = "BondingSingle2023.Device.Application.json";
            //var symbolFilePath = "BondingSingle2023.Device.Application.json";
            if (!File.Exists(dir_path + symbolFilePath))
            {
                LoadException = true;
                Trace.WriteLine("Symbol file not find");
                ExceptionString = "Symbol file not find";
                yield return null;
            }
            var jsonStr = File.ReadAllText(dir_path + symbolFilePath);
            if (string.IsNullOrEmpty(jsonStr) || string.IsNullOrWhiteSpace(jsonStr))
            {
                LoadException = true;
                Trace.WriteLine("Symbol table is empty");

                ExceptionString = "Symbol table is empty";

                yield return null;
            }
            symbols = JsonSerializer.Deserialize<Symbols>(jsonStr);

            uint handler = 0;
            foreach (var item in symbols.GetSymbols())
            {
                if (item.Name is not null && item.Address is not null && item.Type is not null)
                {
                    var symbol = new VariableSymbol(handler++, item);
                    yield return symbol;
                }
            };


        }

        public bool KeepAliveRequest()
        {
            try
            {
                // 使用超时保护，防止阻塞UI线程
                var task = Task.Run(() => Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, 0, 1, new byte[2], 0));
                if (task.Wait(1000)) // 最多等待1秒
                {
                    return task.Result == 1;
                }
                else
                {
                    // 超时，认为连接失败
                    return false;
                }
            }
            catch (Exception)
            {
                // 发生异常，认为连接失败
                return false;
            }
        }

        public void LoadSymbols()
        {
            try
            {
                foreach (var item in ParseFromJson())
                {
                    if (item == null && LoadException)
                    {
                        break;
                    }
                    else
                    {

                        Symbols.Add(item);
                    }
                }

                if (LoadException)
                {
                    Task.Run(() =>
                    {
                        throw new ExternalException(ExceptionString);
                    });
                }
            }
            catch (ExternalException e)
            {
                _logger?.LogError(e, $"加载PLC符号表出错: {e.Message}", WaferAligner.EventIds.EventIds.PlcVariableReadError);
            }
        }

        public EventHandler<InvoanceVariableChangedEventArgs> HCNotificationChanged = (s, e) => { };

        public EnumErrorCode AddDeviceNotificationEx(string symbolPath, NotificationSettings settings, object? userData, Type type, out uint notificationHandler)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException("客户端链接已被释放");
            }
            var s = Symbols.Find(s => s.Name == symbolPath);

            if (s != null)
            {
                if (s.Address < 100)
                {
                    notificationHandler = uint.MaxValue;
                    se.Release();
                    return EnumErrorCode.None;
                }
                notificationHandler = s.Handler;
                try
                {
                    lock (slices)
                    {
                        var ret = slices.Insert(s.Handler, s.Address, s.Size);

                        if (ret)
                        {
                            return TryAddDeviceNotificationEX(symbolPath, settings, userData, type, null);
                        }
                        else
                        {
                            return EnumErrorCode.ER_ELEM_ADDR_OVER;
                        }
                    }

                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"添加设备通知失败: {symbolPath}", WaferAligner.EventIds.EventIds.PlcVariableReadError);
                    throw;
                }
            }
            else
            {
                notificationHandler = uint.MaxValue;
                return EnumErrorCode.ER_SYMBOLNOTFOUND;
            }
        }

        private EnumErrorCode TryAddDeviceNotificationEX(string symbolPath, NotificationSettings settings, object? userData, Type type, int[]? args)
        {
            Symbols.Find(s => s.Name == symbolPath)?.IncreaseNotificationRegistry();

            return EnumErrorCode.None;
        }
        public int WriteData(string name, object value)
        {
            VariableSymbol variableinfo = Symbols.Find(v => v.Name == name);
            if (variableinfo is null)
            {
                return 0;
            }
            var buffer = GetBytes(value, variableinfo.DataType);
            var word_size = variableinfo.Size / 2;
            if (word_size == 0)
            {
                word_size = 1;
            }
            var ret = Am600_Write_Soft_Elem(SoftElemType.ELEM_MW, variableinfo.Address, word_size, buffer, 0);
            if (ret == 0)
            {
                return 0;
            }
            else
            {
                return word_size;
            }
        }

        byte[] GetBytes(object value, Type type)
        {
            switch (type.Name.ToLower())
            {
                case "boolean":
                    var bool_buffer = Convert.ToBoolean(value);
                    return BitConverter.GetBytes(bool_buffer);
                case "byte" or "sbyte":
                    var byte_value = (byte)value;
                    return new byte[] { byte_value };
                case "int16":
                    var int16_buffer = Convert.ToInt16(value);
                    return BitConverter.GetBytes(int16_buffer);
                case "uint16":
                    var uint16_buffer = Convert.ToUInt16(value);
                    return BitConverter.GetBytes(uint16_buffer);
                case "int32":
                    var int32 = Convert.ToInt32(value);
                    return BitConverter.GetBytes(int32);
                case "uint32":
                    var uint32_buffer = Convert.ToUInt32(value);
                    return BitConverter.GetBytes(uint32_buffer);
                case "int64":
                    var int64_buffer = Convert.ToInt64(value);
                    return BitConverter.GetBytes(int64_buffer);
                case "uint64":
                    var uint64_buffer = Convert.ToUInt64(value);
                    return BitConverter.GetBytes(uint64_buffer);
                case "single":
                    var single_buffer = Convert.ToSingle(value);
                    return BitConverter.GetBytes(single_buffer);
                case "double":
                    var double_buffer = Convert.ToDouble(value);
                    return BitConverter.GetBytes(double_buffer);
                default:
                    return null;
            };
        }

        public object? ReadDataAsync(string name, Type type)
        {
            VariableSymbol variableinfo = Symbols.Find(v => v.Name == name);
            var buffer = new byte[8];
            var word_size = variableinfo.Size / 2;
            if (word_size == 0)
            {
                word_size = 1;
            }
            var ret = Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, variableinfo.Address, word_size, buffer, 0);
            if (ret == 0)
            {
                return null;
            }
            var span = new Span<byte>(buffer, 0, variableinfo.Size);

            return CastValue(span, variableinfo.DataType);
        }

        private bool _disposed;
        public bool isDisposed => _disposed;
        public void Dispose()
        {
            if (!_disposed)
            {
                Dispose(disposing: true);
            }
            _disposed = true;
            GC.SuppressFinalize(this);
        }
        private void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 取消并等待查询任务完成
                    if (_queryTaskCts != null)
                    {
                        _queryTaskCts.Cancel();
                        
                        try
                        {
                            // 等待最多1秒钟让任务结束
                            if (_queryTask != null)
                            {
                                Task.WaitAny(_queryTask, Task.Delay(1000));
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogWarning(ex, $"等待PLC查询任务结束时发生异常", WaferAligner.EventIds.EventIds.ResourceReleased);
                        }
                        
                        _queryTaskCts.Dispose();
                        _queryTaskCts = null;
                    }
                    
                    cts.Cancel();
                    cts.Dispose();
                    Disconnect();
                    _logger?.LogInformation("已释放PLC客户端资源", WaferAligner.EventIds.EventIds.ResourceReleased);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"释放PLC资源时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.UnhandledException);
                }
            }
        }
        private void Disconnect()
        {
            try
            {
                Exit_ETH();
                _logger?.LogInformation("已断开PLC连接", WaferAligner.EventIds.EventIds.PlcDisconnectComplete);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"断开PLC连接时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.PlcDisconnectComplete);
            }
        }
        ~InvoancePlcClient()
        {
            Dispose(disposing: false);
        }
    }
}
