{"version": 3, "targets": {"net6.0-windows7.0": {"Beckhoff.TwinCAT.Ads/6.0.216": {"type": "package", "dependencies": {"Beckhoff.TwinCAT.Ads.Server": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.ComponentModel.Composition": "6.0.0", "System.Reactive": "5.0.0"}, "compile": {"lib/net6.0/TwinCAT.Ads.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TwinCAT.Ads.dll": {"related": ".xml"}}}, "Beckhoff.TwinCAT.Ads.Abstractions/6.0.216": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net6.0/TwinCAT.Ads.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TwinCAT.Ads.Abstractions.dll": {"related": ".xml"}}}, "Beckhoff.TwinCAT.Ads.Reactive/6.0.216": {"type": "package", "dependencies": {"Beckhoff.TwinCAT.Ads.Abstractions": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Reactive": "5.0.0"}, "compile": {"lib/net6.0/TwinCAT.Ads.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TwinCAT.Ads.Reactive.dll": {"related": ".xml"}}}, "Beckhoff.TwinCAT.Ads.Server/6.0.216": {"type": "package", "dependencies": {"Beckhoff.TwinCAT.Ads.Abstractions": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Reactive": "5.0.0"}, "compile": {"lib/net6.0/TwinCAT.Ads.Server.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TwinCAT.Ads.Server.dll": {"related": ".xml"}}}, "Castle.Core/5.1.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets": {}}}, "Microsoft.ApplicationInsights/2.23.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.5": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets": {}}}, "Microsoft.EntityFrameworkCore/6.0.11": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.11", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.11", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.11": {"type": "package", "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.11": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "System.Text.Json": "9.0.5"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Json": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets": {}}}, "Microsoft.Extensions.Diagnostics/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.5", "System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileSystemGlobbing": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.5": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets": {}}}, "Microsoft.Extensions.Hosting/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.Configuration.CommandLine": "9.0.5", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.5", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.5", "Microsoft.Extensions.Configuration.Json": "9.0.5", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.5", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.Logging.Console": "9.0.5", "Microsoft.Extensions.Logging.Debug": "9.0.5", "Microsoft.Extensions.Logging.EventLog": "9.0.5", "Microsoft.Extensions.Logging.EventSource": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets": {}}}, "Microsoft.Extensions.Logging.Console/9.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Buffers": "4.5.1", "System.Text.Json": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets": {}}}, "Microsoft.Extensions.Logging.EventLog/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Diagnostics.EventLog": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets": {}}}, "Microsoft.Extensions.Logging.EventSource/9.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets": {}}}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5", "System.ComponentModel.Annotations": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Testing.Extensions.Telemetry/1.7.3": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Testing.Platform": "1.7.3"}, "compile": {"lib/net6.0/Microsoft.Testing.Extensions.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Testing.Extensions.Telemetry.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/net6.0/Microsoft.Testing.Extensions.Telemetry.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Extensions.Telemetry.props": {}}}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.7.3": {"type": "package", "dependencies": {"Microsoft.Testing.Platform": "1.7.3"}, "compile": {"lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Testing.Extensions.VSTestBridge/1.7.3": {"type": "package", "dependencies": {"Microsoft.TestPlatform.AdapterUtilities": "17.13.0", "Microsoft.TestPlatform.ObjectModel": "17.13.0", "Microsoft.Testing.Extensions.Telemetry": "1.7.3", "Microsoft.Testing.Extensions.TrxReport.Abstractions": "1.7.3", "Microsoft.Testing.Platform": "1.7.3"}, "compile": {"lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform/1.7.3": {"type": "package", "compile": {"lib/net6.0/Microsoft.Testing.Platform.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Testing.Platform.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.Testing.Platform.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.Testing.Platform.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.Testing.Platform.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.Testing.Platform.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.Testing.Platform.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.Testing.Platform.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.Testing.Platform.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.Testing.Platform.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.Testing.Platform.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.Testing.Platform.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.Testing.Platform.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/net6.0/Microsoft.Testing.Platform.props": {}, "buildTransitive/net6.0/Microsoft.Testing.Platform.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Platform.props": {}, "buildMultiTargeting/Microsoft.Testing.Platform.targets": {}}}, "Microsoft.Testing.Platform.MSBuild/1.7.3": {"type": "package", "dependencies": {"Microsoft.Testing.Platform": "1.7.3"}, "compile": {"lib/net6.0/Microsoft.Testing.Extensions.MSBuild.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Testing.Extensions.MSBuild.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/Microsoft.Testing.Platform.MSBuild.props": {}, "buildTransitive/Microsoft.Testing.Platform.MSBuild.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.props": {}, "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.targets": {}}}, "Microsoft.TestPlatform.AdapterUtilities/17.13.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll": {}}, "runtime": {"lib/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll": {}}, "resource": {"lib/net6.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.ObjectModel/17.13.0": {"type": "package", "dependencies": {"System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Moq/4.20.72": {"type": "package", "dependencies": {"Castle.Core": "5.1.1"}, "compile": {"lib/net6.0/Moq.dll": {}}, "runtime": {"lib/net6.0/Moq.dll": {}}}, "MSTest.Analyzers/3.9.3": {"type": "package", "build": {"buildTransitive/MSTest.Analyzers.props": {}, "buildTransitive/MSTest.Analyzers.targets": {}}}, "MSTest.TestAdapter/3.9.3": {"type": "package", "dependencies": {"Microsoft.Testing.Extensions.VSTestBridge": "1.7.3", "Microsoft.Testing.Platform.MSBuild": "1.7.3"}, "build": {"buildTransitive/net6.0/MSTest.TestAdapter.props": {}, "buildTransitive/net6.0/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/3.9.3": {"type": "package", "dependencies": {"MSTest.Analyzers": "3.9.3"}, "compile": {"lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/net6.0/MSTest.TestFramework.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.native.System.IO.Ports/9.0.7": {"type": "package", "dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.7"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "SunnyUI/*******": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.0"}, "compile": {"lib/net6.0-windows7.0/SunnyUI.dll": {}}, "runtime": {"lib/net6.0-windows7.0/SunnyUI.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "SunnyUI.Common/3.8.0": {"type": "package", "compile": {"lib/net6.0/SunnyUI.Common.dll": {}}, "runtime": {"lib/net6.0/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections.Immutable/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.ComponentModel.Composition/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.ComponentModel.Composition.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ComponentModel.Composition.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets": {}}}, "System.Diagnostics.EventLog/9.0.5": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets": {}}}, "System.Drawing.Common/6.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "compile": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Pipelines/9.0.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets": {}}}, "System.IO.Ports/9.0.7": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "9.0.7"}, "compile": {"lib/netstandard2.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.IO.Ports.targets": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reactive/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Metadata/1.6.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "compile": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/9.0.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets": {}}}, "System.Text.Json/9.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.5", "System.Buffers": "4.5.1", "System.IO.Pipelines": "9.0.5", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "9.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Text.Json.targets": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Windows.Extensions/6.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "Aya.Extension/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"System.Reactive": "5.0.0", "System.Reflection": "4.3.0"}, "compile": {"bin/placeholder/Aya.Extension.dll": {}}, "runtime": {"bin/placeholder/Aya.Extension.dll": {}}}, "Aya.Log/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"JYJ001.App.Service.Common.Interface": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"bin/placeholder/Aya.Log.dll": {}}, "runtime": {"bin/placeholder/Aya.Log.dll": {}}}, "JYJ001.App.Business/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Reactive": "5.0.0"}, "compile": {"bin/placeholder/JYJ001.App.Business.dll": {}}, "runtime": {"bin/placeholder/JYJ001.App.Business.dll": {}}}, "JYJ001.App.Service.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Aya.Extension": "1.0.0", "Aya.Log": "1.0.0", "JYJ001.App.Service.Common.Extension": "1.0.0", "JYJ001.App.Service.Common.Interface": "1.0.0", "Newtonsoft.Json": "13.0.3", "System.Reactive": "5.0.0", "WaferAligner.EventIds": "1.0.0"}, "compile": {"bin/placeholder/JYJ001.App.Service.Common.dll": {}}, "runtime": {"bin/placeholder/JYJ001.App.Service.Common.dll": {}}}, "JYJ001.App.Service.Common.Extension/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"JYJ001.App.Service.Common.Interface": "1.0.0"}, "compile": {"bin/placeholder/JYJ001.App.Service.Common.Extension.dll": {}}, "runtime": {"bin/placeholder/JYJ001.App.Service.Common.Extension.dll": {}}}, "JYJ001.App.Service.Common.Interface/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"JYJ001.App.Business": "1.0.0", "Microsoft.EntityFrameworkCore": "6.0.11"}, "compile": {"bin/placeholder/JYJ001.App.Service.Common.Interface.dll": {}}, "runtime": {"bin/placeholder/JYJ001.App.Service.Common.Interface.dll": {}}}, "JYJ001.App.Service.Usermanagement/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"JYJ001.App.Service.Common": "1.0.0", "JYJ001.App.Service.Common.Interface": "1.0.0", "Newtonsoft.Json": "13.0.3", "WaferAligner.EventIds": "1.0.0"}, "compile": {"bin/placeholder/JYJ001.App.Service.Usermanagement.dll": {}}, "runtime": {"bin/placeholder/JYJ001.App.Service.Usermanagement.dll": {}}}, "WaferAligner.Communication.Abstractions/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Beckhoff.TwinCAT.Ads": "6.0.216", "Beckhoff.TwinCAT.Ads.Reactive": "6.0.216"}, "compile": {"bin/placeholder/WaferAligner.Communication.Abstractions.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Communication.Abstractions.dll": {}}}, "WaferAligner.Communication.Inovance/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"WaferAligner.Communication.Abstractions": "1.0.0", "WaferAligner.EventIds": "1.0.0", "WaferAligner.Infrastructure.Logging": "1.0.0"}, "compile": {"bin/placeholder/WaferAligner.Communication.Inovance.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Communication.Inovance.dll": {}}}, "WaferAligner.Core.Business/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Reactive": "5.0.0"}, "compile": {"bin/placeholder/WaferAligner.Core.Business.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Core.Business.dll": {}}}, "WaferAligner.Core.DataObserver/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"System.Reactive": "5.0.0", "WaferAligner.Core.Business": "1.0.0"}, "compile": {"bin/placeholder/WaferAligner.Core.DataObserver.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Core.DataObserver.dll": {}}}, "WaferAligner.Core.Events/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "compile": {"bin/placeholder/WaferAligner.Core.Events.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Core.Events.dll": {}}}, "WaferAligner.EventIds/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "compile": {"bin/placeholder/WaferAligner.EventIds.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.EventIds.dll": {}}}, "WaferAligner.Infrastructure.Extensions/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"System.Reactive": "5.0.0", "System.Reflection": "4.3.0"}, "compile": {"bin/placeholder/WaferAligner.Infrastructure.Extensions.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Infrastructure.Extensions.dll": {}}}, "WaferAligner.Infrastructure.Logging/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"bin/placeholder/WaferAligner.Infrastructure.Logging.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Infrastructure.Logging.dll": {}}}, "WaferAligner.Services.Abstractions/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"JYJ001.App.Business": "1.0.0", "Microsoft.EntityFrameworkCore": "6.0.11"}, "compile": {"bin/placeholder/WaferAligner.Services.Abstractions.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Services.Abstractions.dll": {}}}, "WaferAligner.Services.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Newtonsoft.Json": "13.0.3", "System.Reactive": "5.0.0", "WaferAligner.EventIds": "1.0.0", "WaferAligner.Infrastructure.Extensions": "1.0.0", "WaferAligner.Infrastructure.Logging": "1.0.0", "WaferAligner.Services.Abstractions": "1.0.0", "WaferAligner.Services.Extensions": "1.0.0"}, "compile": {"bin/placeholder/WaferAligner.Services.Core.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Services.Core.dll": {}}}, "WaferAligner.Services.Extensions/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"WaferAligner.Services.Abstractions": "1.0.0"}, "compile": {"bin/placeholder/WaferAligner.Services.Extensions.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Services.Extensions.dll": {}}}}}, "libraries": {"Beckhoff.TwinCAT.Ads/6.0.216": {"sha512": "V+LccEj1k31u8pD6ETxrht/oK2aMhPRJ7SZnNYLkUucd2q3A2D+KcUYWTm2PGCrCkosohnf/VGbGAfl5EudW0g==", "type": "package", "path": "beckhoff.twincat.ads/6.0.216", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Package.png", "ReadMe.md", "beckhoff.twincat.ads.6.0.216.nupkg.sha512", "beckhoff.twincat.ads.nuspec", "lib/net461/TwinCAT.Ads.dll", "lib/net461/TwinCAT.Ads.xml", "lib/net6.0/TwinCAT.Ads.dll", "lib/net6.0/TwinCAT.Ads.xml", "lib/netcoreapp3.1/TwinCAT.Ads.dll", "lib/netcoreapp3.1/TwinCAT.Ads.xml", "lib/netstandard2.0/TwinCAT.Ads.dll", "lib/netstandard2.0/TwinCAT.Ads.xml"]}, "Beckhoff.TwinCAT.Ads.Abstractions/6.0.216": {"sha512": "16yaujxvLpWsNk+PUny75sUfH4oN8FUvszwGqEZ4BeScVjgGeMjCeOzIHrP8PN+tLzAxfiR5TN/JSKfhNmaICw==", "type": "package", "path": "beckhoff.twincat.ads.abstractions/6.0.216", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Package.png", "ReadMe.md", "beckhoff.twincat.ads.abstractions.6.0.216.nupkg.sha512", "beckhoff.twincat.ads.abstractions.nuspec", "lib/net461/TwinCAT.Ads.Abstractions.dll", "lib/net461/TwinCAT.Ads.Abstractions.xml", "lib/net6.0/TwinCAT.Ads.Abstractions.dll", "lib/net6.0/TwinCAT.Ads.Abstractions.xml", "lib/netcoreapp3.1/TwinCAT.Ads.Abstractions.dll", "lib/netcoreapp3.1/TwinCAT.Ads.Abstractions.xml", "lib/netstandard2.0/TwinCAT.Ads.Abstractions.dll", "lib/netstandard2.0/TwinCAT.Ads.Abstractions.xml"]}, "Beckhoff.TwinCAT.Ads.Reactive/6.0.216": {"sha512": "Nf7HUg4+x4B12u2wM0z8jNzR9U9JM0NiiQIu3n/EyU4sUN2pr4J+bPaBKL9JYn4agV4nUT0RIf4jdMXTtYoGVg==", "type": "package", "path": "beckhoff.twincat.ads.reactive/6.0.216", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Package.png", "ReadMe.md", "beckhoff.twincat.ads.reactive.6.0.216.nupkg.sha512", "beckhoff.twincat.ads.reactive.nuspec", "lib/net461/TwinCAT.Ads.Reactive.dll", "lib/net461/TwinCAT.Ads.Reactive.xml", "lib/net6.0/TwinCAT.Ads.Reactive.dll", "lib/net6.0/TwinCAT.Ads.Reactive.xml", "lib/netcoreapp3.1/TwinCAT.Ads.Reactive.dll", "lib/netcoreapp3.1/TwinCAT.Ads.Reactive.xml", "lib/netstandard2.0/TwinCAT.Ads.Reactive.dll", "lib/netstandard2.0/TwinCAT.Ads.Reactive.xml"]}, "Beckhoff.TwinCAT.Ads.Server/6.0.216": {"sha512": "ZpNtECkCWfr4CQ7tuo7ps5uw9frs11wD9kzbsu0doHK5B+YrHEUcZh6uKpGAfCZyU2TBv2UlLtCBhQdC8WwD4w==", "type": "package", "path": "beckhoff.twincat.ads.server/6.0.216", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "Package.png", "ReadMe.md", "beckhoff.twincat.ads.server.6.0.216.nupkg.sha512", "beckhoff.twincat.ads.server.nuspec", "lib/net461/TwinCAT.Ads.Server.dll", "lib/net461/TwinCAT.Ads.Server.xml", "lib/net6.0/TwinCAT.Ads.Server.dll", "lib/net6.0/TwinCAT.Ads.Server.xml", "lib/netcoreapp3.1/TwinCAT.Ads.Server.dll", "lib/netcoreapp3.1/TwinCAT.Ads.Server.xml", "lib/netstandard2.0/TwinCAT.Ads.Server.dll", "lib/netstandard2.0/TwinCAT.Ads.Server.xml"]}, "Castle.Core/5.1.1": {"sha512": "rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "type": "package", "path": "castle.core/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.1.1.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "CommunityToolkit.Mvvm/8.2.2": {"sha512": "r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "type": "package", "path": "communitytoolkit.mvvm/8.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/netstandard2.0/CommunityToolkit.Mvvm.targets", "build/netstandard2.1/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.0/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.2.2.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net6.0/CommunityToolkit.Mvvm.dll", "lib/net6.0/CommunityToolkit.Mvvm.pdb", "lib/net6.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Microsoft.ApplicationInsights/2.23.0": {"sha512": "nWArUZTdU7iqZLycLKWe0TDms48KKGE6pONH2terYNa8REXiqixrMOkf1sk5DHGMaUTqONU2YkS4SAXBhLStgw==", "type": "package", "path": "microsoft.applicationinsights/2.23.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net452/Microsoft.ApplicationInsights.dll", "lib/net452/Microsoft.ApplicationInsights.pdb", "lib/net452/Microsoft.ApplicationInsights.xml", "lib/net46/Microsoft.ApplicationInsights.dll", "lib/net46/Microsoft.ApplicationInsights.pdb", "lib/net46/Microsoft.ApplicationInsights.xml", "lib/netstandard2.0/Microsoft.ApplicationInsights.dll", "lib/netstandard2.0/Microsoft.ApplicationInsights.pdb", "lib/netstandard2.0/Microsoft.ApplicationInsights.xml", "microsoft.applicationinsights.2.23.0.nupkg.sha512", "microsoft.applicationinsights.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/9.0.5": {"sha512": "eNQDjbtFj8kOLxbckCbn2JXTsnzK8+xkA4jg7NULO9jhIvlOSngC9BFzmiqVPpw1INQaP1pQ3YteY2XhfWNjtQ==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.9.0.5.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.EntityFrameworkCore/6.0.11": {"sha512": "eUsIZ52uBJFCr/OUL1EHp0BAwdkfHFVGMyXYrkGUjkSWtPd751wgFzgWBstxOQYzUEyKtz1/wC72S8Db0vPvsg==", "type": "package", "path": "microsoft.entityframeworkcore/6.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props", "lib/net6.0/Microsoft.EntityFrameworkCore.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.6.0.11.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.11": {"sha512": "KJCJjFMZFGYy0G8a8ZUwAe9n/l6P+dP3i4fQJmR4jR0/EFnlfeNeWh8n6nRhP+9YmNz290twaIZSbRoiGU6S2A==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/6.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.6.0.11.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.11": {"sha512": "xke0hphu+BSBwt6Kfv/XERe3s1G7BZjNUByyNj0oIZVD1KPaIhMQJBKHtblkCI04cMnO1Ac2NMEgO67rM+cP/w==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/6.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.6.0.11.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"sha512": "bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "type": "package", "path": "microsoft.extensions.caching.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net461/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"sha512": "B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "type": "package", "path": "microsoft.extensions.caching.memory/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Memory.dll", "lib/net461/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.5": {"sha512": "uYXLg2Gt8KUH5nT3u+TBpg9VrRcN5+2zPmIjqEHR4kOoBwsbtMDncEJw9HiLvZqGgIo2TR4oraibAoy5hXn2bQ==", "type": "package", "path": "microsoft.extensions.configuration/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"sha512": "ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"sha512": "7pQ4Tkyofm8DFWFhqn9ZmG8qSAC2VitWleATj5qob9V9KtoxCVdwRtmiVl/ha3WAgjkEfW++JLWXox9MJwMgkg==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/9.0.5": {"sha512": "BloAPG22eV+F4CpGKg0lHeXsLxbsGeId4mNpNsUc250j79pcJL3OWVRgmyIUBP5eF74lYJlaOVF+54MRBAQV3A==", "type": "package", "path": "microsoft.extensions.configuration.commandline/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.5": {"sha512": "kfLv3nbn3tt42g/YfPMJGW6SJRt4DLIvSu5njrZv622kBGVOXBMwyoqFLvR/tULzn0mwICJu6GORdUJ+INpexg==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.5": {"sha512": "ifrA7POOJ7EeoEJhC8r03WufBsEV4zgnTLQURHh1QIS/vU6ff/60z8M4tD3i2csdFPREEc1nGbiOZhi7Q5aMfw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.5": {"sha512": "LiWV+Sn5yvoQEd/vihGwkR3CZ4ekMrqP5OQiYOlbzMBfBa6JHBWBsTO5ta6dMYO9ADMiv9K6GBKJSF9DrP29sw==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.5": {"sha512": "DONkv4TzvCUps55pu+667HasjhW5WoKndDPt9AvnF3qnYfgh+OXN01cDdH0h9cfXUXluzAZfGhqh/Uwt14aikg==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"sha512": "N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/9.0.5": {"sha512": "fRiUjmhm9e4vMp6WEO9MgWNxVtWSr4Pcgh1W4DyJIr8bRANlZz9JU7uicf7ShzMspDxo/9Ejo9zJ6qQZY0IhVw==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.5.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"sha512": "6YfTcULCYREMTqtk+s3UiszsFV2xN2FXtxdQpurmQJY9Cp/QGiM4MTKfJKUo7AzdLuzjOKKMWjQITmvtK7AsUg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"sha512": "LLm+e8lvD+jOI+blHRSxPqywPaohOTNcVzQv548R1UpkEiNB2D+zf3RrqxBdB1LDPicRMTnfiaKJovxF8oX1bQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.5": {"sha512": "cMQqvK0rclKzAm2crSFe9JiimR+wzt6eaoRxa8/mYFkqekY4JEP8eShVZs4NPsKV2HQFHfDgwfFSsWUrUgqbKA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.5.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.5": {"sha512": "TWJZJGIyUncH4Ah+Sy9X5mPJeoz02lRlFx9VWaFo4b4o0tkA1dk2u6HRHrfEC2L6N4IC+vFzfRWol1egyQqLtg==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.5.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/9.0.5": {"sha512": "PoTG6ptucJyxrrALQgRE5lwUMaSc3PK5vtEXuazEJ6mDQ9xRFmxElZCe81duH/TNH7+X/CVDVIZu6Ji2OQW4zQ==", "type": "package", "path": "microsoft.extensions.hosting/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/net9.0/Microsoft.Extensions.Hosting.dll", "lib/net9.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.9.0.5.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"sha512": "3GA/dxqkP6yFe18qYRgtKYuN2onC8NfhlpNN21jptkVKk7olqBTkdT49oL0pSEz2SptRsux7LocCU7+alGnEag==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.5": {"sha512": "rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "type": "package", "path": "microsoft.extensions.logging/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.5.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"sha512": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"sha512": "WgYTJ1/dxdzqaYYMrgC6cZXJVmaoxUmWgsvR9Kg5ZARpy0LMw7fZIZMIiVuaxhItwwFIW0ruhAN+Er2/oVZgmQ==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.5.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/9.0.5": {"sha512": "0BqgvX5y34GOrsJeAypny53OoBnXjyjQCpanrpm7dZawKv5KFk7Tqbu7LFVsRu2T0tLpQ2YHMciMiAWtp+o/Bw==", "type": "package", "path": "microsoft.extensions.logging.console/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.9.0.5.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.5": {"sha512": "IyosWdl/NM2LP72zlavSpkZyd1SczzJ+8J4LImlKWF8w/JEbqJuSJey79Wd1lJGsDj7Cik8y4CD1T2mXMIhEVA==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.5.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/9.0.5": {"sha512": "KF+lvi5ZwNd5Oy5V6l0580cQjTi59boF6X4wp+2ozvUGTC4zBBsaDSVicR86pTWsDivmo9UeSlB+QgheGzrpJQ==", "type": "package", "path": "microsoft.extensions.logging.eventlog/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.9.0.5.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/9.0.5": {"sha512": "H4PVv6aDt4jNyZi7MN746GtHPNRjGdH7OrueDViQDBAw/b4incGYEPbUKUACa9HED0vfI4PPaQrzz1Hz5Odh3g==", "type": "package", "path": "microsoft.extensions.logging.eventsource/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.9.0.5.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.5": {"sha512": "vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "type": "package", "path": "microsoft.extensions.options/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.5.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"sha512": "CJbAVdovKPFh2FoKxesu20odRVSbL/vtvzzObnG+5u38sOfzRS2Ncy25id0TjYUGQzMhNnJUHgTUzTMDl/3c9g==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.5.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.5": {"sha512": "b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "type": "package", "path": "microsoft.extensions.primitives/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.5.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Testing.Extensions.Telemetry/1.7.3": {"sha512": "udSTexALlmp01a6a4fNzzpcUyUZOUbVZRRdHotCcnM1pCl3G1wqRWJlD9B6vDaIw29tYBPv7iA+YbVvq61RonA==", "type": "package", "path": "microsoft.testing.extensions.telemetry/1.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net7.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net8.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net9.0/Microsoft.Testing.Extensions.Telemetry.props", "build/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props", "buildMultiTargeting/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net6.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net7.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net8.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net9.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props", "lib/net6.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net6.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net7.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net8.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net9.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net9.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "microsoft.testing.extensions.telemetry.1.7.3.nupkg.sha512", "microsoft.testing.extensions.telemetry.nuspec"]}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.7.3": {"sha512": "dDEETHbX5JQBMIFgBPkX/FmCU4DRQSG+k58QUtz7zTdnEZDafPKH6YHSMoHY3blDiZV/vcSol35Ux3WC7wU+9Q==", "type": "package", "path": "microsoft.testing.extensions.trxreport.abstractions/1.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net7.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net7.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net9.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net9.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "microsoft.testing.extensions.trxreport.abstractions.1.7.3.nupkg.sha512", "microsoft.testing.extensions.trxreport.abstractions.nuspec"]}, "Microsoft.Testing.Extensions.VSTestBridge/1.7.3": {"sha512": "iLD8UlqQis9eVmgKxuqEUBm1Tzmsqj6xluj8xe6/scM+PQ84eDX2oqi0rra2N0JLli5LxloTb0iWYN5y4+kn1A==", "type": "package", "path": "microsoft.testing.extensions.vstestbridge/1.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net7.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net9.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net9.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "microsoft.testing.extensions.vstestbridge.1.7.3.nupkg.sha512", "microsoft.testing.extensions.vstestbridge.nuspec"]}, "Microsoft.Testing.Platform/1.7.3": {"sha512": "cI6u+CPxv3+07cbSwJVKOfxFrecbjfZnid1fe8EMhyPY4qmsSNnm+hN+GIy8u4JIlrADfrskDiwnScjRzVzJNw==", "type": "package", "path": "microsoft.testing.platform/1.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/Microsoft.Testing.Platform.props", "build/net6.0/Microsoft.Testing.Platform.targets", "build/net7.0/Microsoft.Testing.Platform.props", "build/net7.0/Microsoft.Testing.Platform.targets", "build/net8.0/Microsoft.Testing.Platform.props", "build/net8.0/Microsoft.Testing.Platform.targets", "build/net9.0/Microsoft.Testing.Platform.props", "build/net9.0/Microsoft.Testing.Platform.targets", "build/netstandard2.0/Microsoft.Testing.Platform.props", "build/netstandard2.0/Microsoft.Testing.Platform.targets", "buildMultiTargeting/Microsoft.Testing.Platform.props", "buildMultiTargeting/Microsoft.Testing.Platform.targets", "buildTransitive/net6.0/Microsoft.Testing.Platform.props", "buildTransitive/net6.0/Microsoft.Testing.Platform.targets", "buildTransitive/net7.0/Microsoft.Testing.Platform.props", "buildTransitive/net7.0/Microsoft.Testing.Platform.targets", "buildTransitive/net8.0/Microsoft.Testing.Platform.props", "buildTransitive/net8.0/Microsoft.Testing.Platform.targets", "buildTransitive/net9.0/Microsoft.Testing.Platform.props", "buildTransitive/net9.0/Microsoft.Testing.Platform.targets", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.targets", "lib/net6.0/Microsoft.Testing.Platform.dll", "lib/net6.0/Microsoft.Testing.Platform.xml", "lib/net6.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/Microsoft.Testing.Platform.dll", "lib/net7.0/Microsoft.Testing.Platform.xml", "lib/net7.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/Microsoft.Testing.Platform.dll", "lib/net8.0/Microsoft.Testing.Platform.xml", "lib/net8.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/Microsoft.Testing.Platform.dll", "lib/net9.0/Microsoft.Testing.Platform.xml", "lib/net9.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "microsoft.testing.platform.1.7.3.nupkg.sha512", "microsoft.testing.platform.nuspec"]}, "Microsoft.Testing.Platform.MSBuild/1.7.3": {"sha512": "tSIKXv7tLLYDjfodqLuTigsmOVcYj0CDC1rYeac5MTgHjbYV8IfmYh4FprBt/xE1zW8phkCYP766F9ayo560jA==", "type": "package", "path": "microsoft.testing.platform.msbuild/1.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.MSBuild.xml", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.dll", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.xml", "_MSBuildTasks/netstandard2.0/Microsoft.Win32.Registry.dll", "_MSBuildTasks/netstandard2.0/System.Security.AccessControl.dll", "_MSBuildTasks/netstandard2.0/System.Security.Principal.Windows.dll", "_MSBuildTasks/netstandard2.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/cs/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/de/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/es/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/fr/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/it/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/ja/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/ko/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/pl/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/ru/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/tr/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "_MSBuildTasks/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "_MSBuildTasks/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "build/Microsoft.Testing.Platform.MSBuild.props", "build/Microsoft.Testing.Platform.MSBuild.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.CustomTestTarget.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.VSTest.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.props", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/Microsoft.Testing.Platform.MSBuild.targets", "lib/net6.0/Microsoft.Testing.Extensions.MSBuild.dll", "lib/net6.0/Microsoft.Testing.Extensions.MSBuild.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.MSBuild.dll", "lib/net7.0/Microsoft.Testing.Extensions.MSBuild.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.MSBuild.dll", "lib/net8.0/Microsoft.Testing.Extensions.MSBuild.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/Microsoft.Testing.Extensions.MSBuild.dll", "lib/net9.0/Microsoft.Testing.Extensions.MSBuild.xml", "lib/net9.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.MSBuild.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.MSBuild.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.MSBuild.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll", "microsoft.testing.platform.msbuild.1.7.3.nupkg.sha512", "microsoft.testing.platform.msbuild.nuspec"]}, "Microsoft.TestPlatform.AdapterUtilities/17.13.0": {"sha512": "bFZ3uAhosdXjyXKURDQy37fPosCJQwedB5DG/SzsXL1QXsrfsIYty2kQMqCRRUqm8sBZBRHWRp4BT9SmpWXcKQ==", "type": "package", "path": "microsoft.testplatform.adapterutilities/17.13.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/Microsoft.TestPlatform.AdapterUtilities.dll", "lib/net462/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll", "lib/net6.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net6.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/Microsoft.TestPlatform.AdapterUtilities.dll", "lib/net8.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net8.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/Microsoft.TestPlatform.AdapterUtilities.dll", "lib/net9.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/net9.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.AdapterUtilities.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "microsoft.testplatform.adapterutilities.17.13.0.nupkg.sha512", "microsoft.testplatform.adapterutilities.nuspec"]}, "Microsoft.TestPlatform.ObjectModel/17.13.0": {"sha512": "bt0E0Dx+iqW97o4A59RCmUmz/5NarJ7LRL+jXbSHod72ibL5XdNm1Ke+UO5tFhBG4VwHLcSjqq9BUSblGNWamw==", "type": "package", "path": "microsoft.testplatform.objectmodel/17.13.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "microsoft.testplatform.objectmodel.17.13.0.nupkg.sha512", "microsoft.testplatform.objectmodel.nuspec"]}, "Microsoft.Win32.SystemEvents/6.0.0": {"sha512": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "type": "package", "path": "microsoft.win32.systemevents/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.6.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Moq/4.20.72": {"sha512": "EA55cjyNn8eTNWrgrdZJH5QLFp2L43oxl1tlkoYUKIE9pRwL784OWiTXeCV5ApS+AMYEAlt7Fo03A2XfouvHmQ==", "type": "package", "path": "moq/4.20.72", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/Moq.dll", "lib/net6.0/Moq.dll", "lib/netstandard2.0/Moq.dll", "lib/netstandard2.1/Moq.dll", "moq.4.20.72.nupkg.sha512", "moq.nuspec", "readme.md"]}, "MSTest.Analyzers/3.9.3": {"sha512": "M8dR1yQZErWiH8BtQT1L66xnuRbSLa8WgQLwI/A0yU/rFmEAorbU+hUj3JeJRqyVFwrDpjeQ4rmg7Vo6zSgzRw==", "type": "package", "path": "mstest.analyzers/3.9.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "analyzers/dotnet/cs/MSTest.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/MSTest.Analyzers.dll", "analyzers/dotnet/cs/cs/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/cs/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/de/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/de/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/es/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/es/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/fr/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/it/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/it/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/ja/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/ko/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/pl/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/pt-BR/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/ru/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/tr/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hans/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/zh-Hans/MSTest.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/MSTest.Analyzers.CodeFixes.resources.dll", "analyzers/dotnet/cs/zh-Hant/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/MSTest.Analyzers.dll", "analyzers/dotnet/vb/cs/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/de/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/es/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/it/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hans/MSTest.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/MSTest.Analyzers.resources.dll", "buildTransitive/MSTest.Analyzers.props", "buildTransitive/MSTest.Analyzers.targets", "globalconfigs/mstest-all.globalconfig", "globalconfigs/mstest-default.globalconfig", "globalconfigs/mstest-none.globalconfig", "globalconfigs/mstest-recommended.globalconfig", "mstest.analyzers.3.9.3.nupkg.sha512", "mstest.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "MSTest.TestAdapter/3.9.3": {"sha512": "smeoTZladaMNSB+q8TeTcFdk2hA5hxdqbSnvEiuTo96v/pJlllPc9PviggjXAY6n3aO+7wKC8GXH66c5QPPptg==", "type": "package", "path": "mstest.testadapter/3.9.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net462/MSTest.TestAdapter.props", "build/net462/MSTest.TestAdapter.targets", "build/net6.0/MSTest.TestAdapter.props", "build/net6.0/MSTest.TestAdapter.targets", "build/net7.0/MSTest.TestAdapter.props", "build/net7.0/MSTest.TestAdapter.targets", "build/net8.0/MSTest.TestAdapter.props", "build/net8.0/MSTest.TestAdapter.targets", "build/net9.0/MSTest.TestAdapter.props", "build/net9.0/MSTest.TestAdapter.targets", "build/netcoreapp3.1/MSTest.TestAdapter.props", "build/netcoreapp3.1/MSTest.TestAdapter.targets", "build/netstandard2.0/MSTest.TestAdapter.props", "build/netstandard2.0/MSTest.TestAdapter.targets", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "buildTransitive/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "buildTransitive/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "buildTransitive/net462/MSTest.TestAdapter.props", "buildTransitive/net462/MSTest.TestAdapter.targets", "buildTransitive/net462/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/net462/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net462/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "buildTransitive/net6.0/MSTest.TestAdapter.props", "buildTransitive/net6.0/MSTest.TestAdapter.targets", "buildTransitive/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/net6.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net6.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/net7.0/MSTest.TestAdapter.props", "buildTransitive/net7.0/MSTest.TestAdapter.targets", "buildTransitive/net7.0/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/net7.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net7.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/net8.0/MSTest.TestAdapter.props", "buildTransitive/net8.0/MSTest.TestAdapter.targets", "buildTransitive/net8.0/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/net8.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net8.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/net9.0/MSTest.TestAdapter.props", "buildTransitive/net9.0/MSTest.TestAdapter.targets", "buildTransitive/net9.0/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/net9.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net9.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/net9.0/uwp/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/net9.0/uwp/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/netcoreapp3.1/MSTest.TestAdapter.props", "buildTransitive/netcoreapp3.1/MSTest.TestAdapter.targets", "buildTransitive/netcoreapp3.1/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/netstandard2.0/MSTest.TestAdapter.props", "buildTransitive/netstandard2.0/MSTest.TestAdapter.targets", "buildTransitive/netstandard2.0/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "buildTransitive/uap10.0/MSTest.TestAdapter.props", "buildTransitive/uap10.0/MSTest.TestAdapter.targets", "buildTransitive/uap10.0/Microsoft.TestPlatform.AdapterUtilities.dll", "buildTransitive/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "buildTransitive/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "mstest.testadapter.3.9.3.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/3.9.3": {"sha512": "GkswaCXXGhSx6bHkl01ybBffVtRR/IPgyzIHDWywd6/yVNH96MaGuUeEtpuGaJXHe2sdMBgB2etp6panY4w6pQ==", "type": "package", "path": "mstest.testframework/3.9.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/MSTest.TestFramework.targets", "build/net7.0/MSTest.TestFramework.targets", "build/net8.0/MSTest.TestFramework.targets", "build/net9.0/MSTest.TestFramework.targets", "buildTransitive/net6.0/MSTest.TestFramework.targets", "buildTransitive/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "buildTransitive/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "buildTransitive/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "buildTransitive/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "buildTransitive/net7.0/MSTest.TestFramework.targets", "buildTransitive/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "buildTransitive/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "buildTransitive/net8.0/MSTest.TestFramework.targets", "buildTransitive/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "buildTransitive/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "buildTransitive/net9.0/MSTest.TestFramework.targets", "buildTransitive/net9.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "buildTransitive/net9.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "buildTransitive/net9.0/uwp/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "buildTransitive/net9.0/uwp/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net7.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net9.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net9.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net9.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "mstest.testframework.3.9.3.nupkg.sha512", "mstest.testframework.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.7": {"sha512": "xtOeUEMiUqYbYCbH6LuU7jH0KD0fAdMPjC2OmcXn9bTmZYiBazZBiVKQlekCmeXrBf39siRbx8TNnVI8U9RWVA==", "type": "package", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/libSystem.IO.Ports.Native.so", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "F3SHKwakrr9YenXhjk73bn5W8IQGXpBKTl3qmHUwdmkEKtc8J/30D6OA0/0M1oG3w/YvdgBiWIuy4fnId/98uQ==", "type": "package", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "uRt8zM34o9NRn9PT8/IxPf4eR4wMNeSLoCTtixekALpwg6GKdGn2JxuWoyzRYK513vUgEBl9TXVF0lPEgJnASg==", "type": "package", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.7": {"sha512": "az6Dxw9DZ2I4ypY0cgML6XXKvi3yK1Tevlsv4AdeV0qaD/7hpepuJr2XpPleWF744QHlVIhMlLX1fMcQM0Zr4w==", "type": "package", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x86.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-x86.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.7": {"sha512": "uGkizTYesvArtyzD50BlIY4dlbs36OT0KNsPuwG9wEQvpz4WGiKbUD494Y6nfGJoIFkW0hdqOBljqE234tk26A==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "HTyktEebT2q6Et9XSG5hTWqt69n7hI+0UBHBOPeSlMZ6cMSR/MW5bO1yFqaR/+U6nM08zRCpaBOGOw2GPTq1eg==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "N/aKWbFSvESYOqj5IYfzN9GcRXlTsaeJ899XCwiLGoMZIfLZTF1ejaR6Ikq3VnGdv4vilTSlvsDcxvskXwfKWA==", "type": "package", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "OyEZXmsoXPGu/kC/JFlVVZiVdVQmFxYwVJPR8CjX3SZTru2cTfDmX7v1d+kbuSrgUjrvLjk77PwbFL6Iz4KkPA==", "type": "package", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-bionic-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.7": {"sha512": "AC42my/QpwBBxRGKVqed1k0Kn1iZ5ZGNN/R6VLByKDHaDlUO7W6VUimle3JN9imC5R8aq9or6VpqFu9mCss0Ew==", "type": "package", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-musl-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "xQhCGVeC555KtzeyQnPzWbifovO3mGLiGGUZppluvqAGvygAhiXqrigSMgu0xTowE2u5WwFCIj4Trco2DU9uxg==", "type": "package", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-musl-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "NQElKMyNNzssg+JFQ1LjPuRng5uoVXbt2aftpUaztDxyBhCBRm/9mZjbWvXS5I5E9dhfNUdFWU40dxpo5bITMw==", "type": "package", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-musl-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "wO5758mD+mKBI2V8U/FdfbmcSEW7lLaRohozF72i8x4Ly4o2tmFzYTcjocfJJpTxS8DnK6X9qxP9pQakTwZk7w==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "8tpVCB289sH5/w/lrztQaVWi6s6V0Ptrpmm6YJR670YswgUPauxeEbN4wiUp7lxC00bTTJq6tRfBntQ1ryyALw==", "type": "package", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "qfdGPrRoZcKLSXNO1vGIWJ0ovTh23Zy2cf0MfnNpQK58Dp27eALuVk0p5kpB4X/bf7I9KO+XorezUSFQZ6MHdw==", "type": "package", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.maccatalyst-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.native.System.IO.Ports/9.0.7": {"sha512": "4IJb6UpOGat7IqKl2QAahMYMc1rMWRf9BWeiNDZBsyNViFOtSftgSjUKzFLtnSlXrbc7qqPCh1M9wOE+WNgcOQ==", "type": "package", "path": "runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "pOZCQaB07EYIWxT5zMgoQr0QLbokVWmItEIOoCqbhY7m8ozV21Jxy5u6ZEL8gMO7QWD+zGbRZQYEQpCNAd+J4g==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "aZT8Go/6NcnKce+xwMbrAxNc9rA6K3SKXPiK5A+A/wiUrlZK6CQwYueKDmU3zdm/oSrUfHSzk0YuCBknRRp5wA==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "SunnyUI/*******": {"sha512": "GSxA5gww5k/XvoUBGBMjIparax9TpATcPpbqlPjnQCCOokoDBl4TfZz2AHT7+GKoregsaCjyWjgNGCJgfI+Fdg==", "type": "package", "path": "sunnyui/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.dll", "lib/net472/SunnyUI.dll", "lib/net6.0-windows7.0/SunnyUI.dll", "lib/net8.0-windows7.0/SunnyUI.dll", "lib/net9.0-windows7.0/SunnyUI.dll", "sunnyui.*******.nupkg.sha512", "sunnyui.nuspec"]}, "SunnyUI.Common/3.8.0": {"sha512": "agGB47JW7J7QAiSe70N7oPXOCu5KI/lfvHGAGnDg4aVBIXO/Bluu4bGEKXMR2DRknViDaJ0alJavfKNKql563w==", "type": "package", "path": "sunnyui.common/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.Common.dll", "lib/net472/SunnyUI.Common.dll", "lib/net6.0/SunnyUI.Common.dll", "lib/net8.0/SunnyUI.Common.dll", "lib/net9.0/SunnyUI.Common.dll", "sunnyui.common.3.8.0.nupkg.sha512", "sunnyui.common.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/6.0.0": {"sha512": "l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "type": "package", "path": "system.collections.immutable/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.6.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Composition/6.0.0": {"sha512": "60Qv+F7oxomOjJeTDA5Z4iCyFbQ0B/2Mi5HT+13pxxq0lVnu2ipbWMzFB+RWKr3wWKA8BSncXr9PH/fECwMX5Q==", "type": "package", "path": "system.componentmodel.composition/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/_._", "lib/net6.0/System.ComponentModel.Composition.dll", "lib/net6.0/System.ComponentModel.Composition.xml", "lib/netcoreapp3.1/System.ComponentModel.Composition.dll", "lib/netcoreapp3.1/System.ComponentModel.Composition.xml", "lib/netstandard2.0/System.ComponentModel.Composition.dll", "lib/netstandard2.0/System.ComponentModel.Composition.xml", "system.componentmodel.composition.6.0.0.nupkg.sha512", "system.componentmodel.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/6.0.0": {"sha512": "7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "type": "package", "path": "system.configuration.configurationmanager/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/9.0.6": {"sha512": "nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.5": {"sha512": "nhtTvAgKTD7f6t0bkOb4/hNv0PShb8GHs5Fhn7PvYhwhyWiVyVBvL2vTGH0Hlw5jOZQmWkzQxjY6M/h4tl8M6Q==", "type": "package", "path": "system.diagnostics.eventlog/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.5.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/6.0.0": {"sha512": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "type": "package", "path": "system.drawing.common/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/net461/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/netcoreapp3.1/System.Drawing.Common.dll", "lib/netcoreapp3.1/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "runtimes/unix/lib/net6.0/System.Drawing.Common.xml", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.xml", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.xml", "system.drawing.common.6.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Pipelines/9.0.5": {"sha512": "5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "type": "package", "path": "system.io.pipelines/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.5.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Ports/9.0.7": {"sha512": "IqnvqsPwKmMBffVpm0PtyxHDQ13b7iN7V4BL/uoQiRhsYnBMoWnX5lpEQNJFzP9SsyqqnkWbsM+z/MkQ9EhUxA==", "type": "package", "path": "system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net8.0/System.IO.Ports.dll", "lib/net8.0/System.IO.Ports.xml", "lib/net9.0/System.IO.Ports.dll", "lib/net9.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net8.0/System.IO.Ports.dll", "runtimes/unix/lib/net8.0/System.IO.Ports.xml", "runtimes/unix/lib/net9.0/System.IO.Ports.dll", "runtimes/unix/lib/net9.0/System.IO.Ports.xml", "runtimes/win/lib/net8.0/System.IO.Ports.dll", "runtimes/win/lib/net8.0/System.IO.Ports.xml", "runtimes/win/lib/net9.0/System.IO.Ports.dll", "runtimes/win/lib/net9.0/System.IO.Ports.xml", "system.io.ports.9.0.7.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reactive/5.0.0": {"sha512": "erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "type": "package", "path": "system.reactive/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net5.0/_._", "build/netcoreapp3.1/System.Reactive.dll", "build/netcoreapp3.1/System.Reactive.targets", "build/netcoreapp3.1/System.Reactive.xml", "buildTransitive/net5.0/_._", "buildTransitive/netcoreapp3.1/System.Reactive.targets", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net5.0-windows10.0.19041/System.Reactive.dll", "lib/net5.0-windows10.0.19041/System.Reactive.xml", "lib/net5.0/System.Reactive.dll", "lib/net5.0/System.Reactive.xml", "lib/netcoreapp3.1/_._", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.16299/System.Reactive.dll", "lib/uap10.0.16299/System.Reactive.pri", "lib/uap10.0.16299/System.Reactive.xml", "system.reactive.5.0.0.nupkg.sha512", "system.reactive.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Metadata/1.6.0": {"sha512": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "type": "package", "path": "system.reflection.metadata/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.1.6.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encodings.Web/9.0.5": {"sha512": "HJPmqP2FsE+WVUUlTsZ4IFRSyzw40yz0ubiTnsaqm+Xo5fFZhVRvx6Zn8tLXj92/6pbre6OA4QL2A2vnCSKxJA==", "type": "package", "path": "system.text.encodings.web/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/net9.0/System.Text.Encodings.Web.dll", "lib/net9.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.9.0.5.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.5": {"sha512": "rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "type": "package", "path": "system.text.json/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Windows.Extensions/6.0.0": {"sha512": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "type": "package", "path": "system.windows.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/netcoreapp3.1/System.Windows.Extensions.dll", "lib/netcoreapp3.1/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.xml", "system.windows.extensions.6.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "Aya.Extension/1.0.0": {"type": "project", "path": "../Common/Aya.Extension/Aya.Extension.csproj", "msbuildProject": "../Common/Aya.Extension/Aya.Extension.csproj"}, "Aya.Log/1.0.0": {"type": "project", "path": "../Common/Aya.Log/Aya.Log.csproj", "msbuildProject": "../Common/Aya.Log/Aya.Log.csproj"}, "JYJ001.App.Business/1.0.0": {"type": "project", "path": "../Business/JYJ001.App.Business/JYJ001.App.Business.csproj", "msbuildProject": "../Business/JYJ001.App.Business/JYJ001.App.Business.csproj"}, "JYJ001.App.Service.Common/1.0.0": {"type": "project", "path": "../Services/Service.Common/JYJ001.App.Service.Common/JYJ001.App.Service.Common.csproj", "msbuildProject": "../Services/Service.Common/JYJ001.App.Service.Common/JYJ001.App.Service.Common.csproj"}, "JYJ001.App.Service.Common.Extension/1.0.0": {"type": "project", "path": "../Services/Service.Common/JYJ001.App.Service.Common.Extension/JYJ001.App.Service.Common.Extension.csproj", "msbuildProject": "../Services/Service.Common/JYJ001.App.Service.Common.Extension/JYJ001.App.Service.Common.Extension.csproj"}, "JYJ001.App.Service.Common.Interface/1.0.0": {"type": "project", "path": "../Services/Service.Common/JYJ001.App.Service.Common.Interface/JYJ001.App.Service.Common.Interface.csproj", "msbuildProject": "../Services/Service.Common/JYJ001.App.Service.Common.Interface/JYJ001.App.Service.Common.Interface.csproj"}, "JYJ001.App.Service.Usermanagement/1.0.0": {"type": "project", "path": "../JYJ001.App.Service.Usermanagement/JYJ001.App.Service.Usermanagement.csproj", "msbuildProject": "../JYJ001.App.Service.Usermanagement/JYJ001.App.Service.Usermanagement.csproj"}, "WaferAligner.Communication.Abstractions/1.0.0": {"type": "project", "path": "../src/Communication/WaferAligner.Communication.Abstractions/WaferAligner.Communication.Abstractions.csproj", "msbuildProject": "../src/Communication/WaferAligner.Communication.Abstractions/WaferAligner.Communication.Abstractions.csproj"}, "WaferAligner.Communication.Inovance/1.0.0": {"type": "project", "path": "../src/Communication/WaferAligner.Communication.Inovance/WaferAligner.Communication.Inovance.csproj", "msbuildProject": "../src/Communication/WaferAligner.Communication.Inovance/WaferAligner.Communication.Inovance.csproj"}, "WaferAligner.Core.Business/1.0.0": {"type": "project", "path": "../src/Core/WaferAligner.Core.Business/WaferAligner.Core.Business.csproj", "msbuildProject": "../src/Core/WaferAligner.Core.Business/WaferAligner.Core.Business.csproj"}, "WaferAligner.Core.DataObserver/1.0.0": {"type": "project", "path": "../src/Core/WaferAligner.Core.DataObserver/WaferAligner.Core.DataObserver.csproj", "msbuildProject": "../src/Core/WaferAligner.Core.DataObserver/WaferAligner.Core.DataObserver.csproj"}, "WaferAligner.Core.Events/1.0.0": {"type": "project", "path": "../src/Core/WaferAligner.Core.Events/WaferAligner.Core.Events.csproj", "msbuildProject": "../src/Core/WaferAligner.Core.Events/WaferAligner.Core.Events.csproj"}, "WaferAligner.EventIds/1.0.0": {"type": "project", "path": "../WaferAligner.EventIds/WaferAligner.EventIds.csproj", "msbuildProject": "../WaferAligner.EventIds/WaferAligner.EventIds.csproj"}, "WaferAligner.Infrastructure.Extensions/1.0.0": {"type": "project", "path": "../src/Infrastructure/WaferAligner.Infrastructure.Extensions/WaferAligner.Infrastructure.Extensions.csproj", "msbuildProject": "../src/Infrastructure/WaferAligner.Infrastructure.Extensions/WaferAligner.Infrastructure.Extensions.csproj"}, "WaferAligner.Infrastructure.Logging/1.0.0": {"type": "project", "path": "../src/Infrastructure/WaferAligner.Infrastructure.Logging/WaferAligner.Infrastructure.Logging.csproj", "msbuildProject": "../src/Infrastructure/WaferAligner.Infrastructure.Logging/WaferAligner.Infrastructure.Logging.csproj"}, "WaferAligner.Services.Abstractions/1.0.0": {"type": "project", "path": "../src/Services/WaferAligner.Services.Abstractions/WaferAligner.Services.Abstractions.csproj", "msbuildProject": "../src/Services/WaferAligner.Services.Abstractions/WaferAligner.Services.Abstractions.csproj"}, "WaferAligner.Services.Core/1.0.0": {"type": "project", "path": "../src/Services/WaferAligner.Services.Core/WaferAligner.Services.Core.csproj", "msbuildProject": "../src/Services/WaferAligner.Services.Core/WaferAligner.Services.Core.csproj"}, "WaferAligner.Services.Extensions/1.0.0": {"type": "project", "path": "../src/Services/WaferAligner.Services.Extensions/WaferAligner.Services.Extensions.csproj", "msbuildProject": "../src/Services/WaferAligner.Services.Extensions/WaferAligner.Services.Extensions.csproj"}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["CommunityToolkit.Mvvm >= 8.2.2", "JYJ001.App.Service.Usermanagement >= 1.0.0", "MSTest.TestAdapter >= 3.9.3", "MSTest.TestFramework >= 3.9.3", "Microsoft.Extensions.Hosting >= 9.0.5", "Microsoft.Extensions.Hosting.Abstractions >= 9.0.5", "Microsoft.Extensions.Logging.Abstractions >= 9.0.6", "Moq >= 4.20.72", "SunnyUI >= *******", "SunnyUI.Common >= 3.8.0", "System.IO.Ports >= 9.0.7", "WaferAligner.Communication.Abstractions >= 1.0.0", "WaferAligner.Communication.Inovance >= 1.0.0", "WaferAligner.Core.Business >= 1.0.0", "WaferAligner.Core.DataObserver >= 1.0.0", "WaferAligner.Core.Events >= 1.0.0", "WaferAligner.Infrastructure.Extensions >= 1.0.0", "WaferAligner.Infrastructure.Logging >= 1.0.0", "WaferAligner.Services.Abstractions >= 1.0.0", "WaferAligner.Services.Core >= 1.0.0", "WaferAligner.Services.Extensions >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\WaferAligner\\WaferAligner.csproj", "projectName": "<PERSON><PERSON>er<PERSON>ligne<PERSON>", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\WaferAligner\\WaferAligner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\WaferAligner\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows7.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\JYJ001.App.Service.Usermanagement\\JYJ001.App.Service.Usermanagement.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\JYJ001.App.Service.Usermanagement\\JYJ001.App.Service.Usermanagement.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Core\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Core\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Core\\WaferAligner.Core.DataObserver\\WaferAligner.Core.DataObserver.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Core\\WaferAligner.Core.DataObserver\\WaferAligner.Core.DataObserver.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Core\\WaferAligner.Core.Events\\WaferAligner.Core.Events.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Core\\WaferAligner.Core.Events\\WaferAligner.Core.Events.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Infrastructure\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Infrastructure\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Infrastructure\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Infrastructure\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Services\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Services\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Services\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Services\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Services\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.8.12-nullimple-75\\src\\Services\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows7.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "MSTest.TestAdapter": {"target": "Package", "version": "[3.9.3, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.9.3, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "SunnyUI": {"target": "Package", "version": "[*******, )"}, "SunnyUI.Common": {"target": "Package", "version": "[3.8.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}