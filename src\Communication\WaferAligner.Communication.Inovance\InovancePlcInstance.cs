using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WaferAligner.Communication.Abstractions;
using WaferAligner.EventIds;

namespace WaferAligner.Communication.Inovance
{
    /// <summary>
    /// 汇川PLC实例实现
    /// 支持ModbusTCP协议通信
    /// </summary>
    public class InovancePlcInstance : IPlcInstance
    {
        #region 私有字段

        private static readonly object LockObject = new();
        private static InovancePlcClient? _client;
        private static volatile bool _isConnected = false;
        private static volatile bool _isConnecting = false;
        private static DateTime _lastConnectionAttempt = DateTime.MinValue;
        private static readonly TimeSpan MinConnectionInterval = TimeSpan.FromSeconds(3);

        private readonly ILogger<InovancePlcInstance>? _logger;
        private PlcConnectionInfo? _connectionInfo;

        #endregion

        #region 公共属性

        /// <summary>
        /// PLC连接状态
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 是否正在连接
        /// </summary>
        public bool IsConnecting => _isConnecting;

        /// <summary>
        /// 最后的错误代码
        /// </summary>
        public InovanceErrorCode LastErrorCode { get; private set; } = InovanceErrorCode.None;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public InovancePlcInstance()
        {
            _logger = null;
            InitializeClient();
        }

        /// <summary>
        /// 带日志的构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public InovancePlcInstance(ILogger<InovancePlcInstance> logger)
        {
            _logger = logger;
            InitializeClient();
        }

        #endregion

        #region 连接管理

        /// <summary>
        /// 连接到PLC
        /// </summary>
        /// <param name="connectPath">连接路径（IP地址）</param>
        /// <param name="port">端口号</param>
        /// <param name="netId">网络ID（未使用）</param>
        public void Connect(string connectPath, int port, int netId = 0)
        {
            if (string.IsNullOrWhiteSpace(connectPath))
            {
                throw new ArgumentException("连接路径不能为空", nameof(connectPath));
            }

            _connectionInfo = new PlcConnectionInfo
            {
                ConnectionType = PlcConnectionType.ModbusTcp,
                Address = connectPath,
                Port = port,
                NetId = netId
            };

            ConnectInternal();
        }

        /// <summary>
        /// 使用默认配置连接到PLC
        /// </summary>
        public void Connect()
        {
            if (_connectionInfo == null)
            {
                // 使用默认配置
                _connectionInfo = new PlcConnectionInfo
                {
                    ConnectionType = PlcConnectionType.ModbusTcp,
                    Address = "************",
                    Port = 502,
                    NetId = 0
                };
            }

            ConnectInternal();
        }

        /// <summary>
        /// 断开PLC连接
        /// </summary>
        public void Disconnect()
        {
            lock (LockObject)
            {
                try
                {
                    if (_client != null && _isConnected)
                    {
                        _client.Disconnect();
                        _isConnected = false;
                        _logger?.LogInformation("PLC连接已断开", EventIds.PlcDisconnectComplete);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "断开PLC连接时发生错误", EventIds.PlcDisconnectFailed);
                }
            }
        }

        #endregion

        #region 通知管理

        /// <summary>
        /// 添加通知事件处理器
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <param name="handler">事件处理器</param>
        public void AddNotification<T>(EventHandler<T> handler)
        {
            // 汇川PLC暂不支持通知机制
            _logger?.LogWarning("汇川PLC暂不支持通知机制", EventIds.PlcNotificationNotSupported);
        }

        /// <summary>
        /// 移除通知事件处理器
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <param name="handler">事件处理器</param>
        public void RemoveNotification<T>(EventHandler<T> handler)
        {
            // 汇川PLC暂不支持通知机制
            _logger?.LogWarning("汇川PLC暂不支持通知机制", EventIds.PlcNotificationNotSupported);
        }

        #endregion

        #region 监控变量管理

        /// <summary>
        /// 注册多个监控变量
        /// </summary>
        public IDictionary<uint, (string Name, Type Type)> RegisterMonitorVariables<T, S>(T variableInfo, S settings)
        {
            // 汇川PLC暂不支持变量监控
            _logger?.LogWarning("汇川PLC暂不支持变量监控", EventIds.PlcMonitoringNotSupported);
            return new Dictionary<uint, (string, Type)>();
        }

        /// <summary>
        /// 注册单个监控变量
        /// </summary>
        public (uint Handle, string Name, Type Type) RegisterMonitorVariable<T, S>(T variableInfo, S settings)
        {
            // 汇川PLC暂不支持变量监控
            _logger?.LogWarning("汇川PLC暂不支持变量监控", EventIds.PlcMonitoringNotSupported);
            return (0, string.Empty, typeof(object));
        }

        /// <summary>
        /// 取消注册监控变量
        /// </summary>
        public void UnRegisterMonitorVariables<T>(T variableInfo)
        {
            // 汇川PLC暂不支持变量监控
            _logger?.LogWarning("汇川PLC暂不支持变量监控", EventIds.PlcMonitoringNotSupported);
        }

        #endregion

        #region 变量读写

        /// <summary>
        /// 异步读取变量
        /// </summary>
        public async Task<object?> ReadVariableAsync<TReadInfo>(TReadInfo info, CancellationToken cancellationToken = default)
        {
            if (!_isConnected)
            {
                LastErrorCode = InovanceErrorCode.NotConnected;
                _logger?.LogWarning("PLC未连接，无法读取变量", EventIds.PlcNotConnected);
                return null;
            }

            try
            {
                if (info is PlcVarReadInfo readInfo)
                {
                    var result = await _client!.ReadVariableAsync(readInfo.Name, cancellationToken);
                    if (result != null)
                    {
                        LastErrorCode = InovanceErrorCode.ReadWriteSucceed;
                        _logger?.LogDebug($"成功读取变量: {readInfo.Name} = {result}", EventIds.PlcReadSuccess);
                        return result;
                    }
                    else
                    {
                        LastErrorCode = InovanceErrorCode.ReadWriteFail;
                        _logger?.LogWarning($"读取变量失败: {readInfo.Name}", EventIds.PlcReadFailed);
                        return null;
                    }
                }
                else
                {
                    LastErrorCode = InovanceErrorCode.InvalidParameter;
                    _logger?.LogError("读取变量参数类型错误", EventIds.PlcInvalidParameter);
                    return null;
                }
            }
            catch (OperationCanceledException)
            {
                LastErrorCode = InovanceErrorCode.OperationTimeout;
                _logger?.LogWarning("读取变量操作被取消", EventIds.PlcOperationCancelled);
                return null;
            }
            catch (Exception ex)
            {
                LastErrorCode = InovanceErrorCode.CommunicationException;
                _logger?.LogError(ex, $"读取变量异常: {ex.Message}", EventIds.PlcReadFailed);
                return null;
            }
        }

        /// <summary>
        /// 异步写入变量
        /// </summary>
        public async Task<bool> WriteVariableAsync<TWriteInfo>(TWriteInfo info, CancellationToken cancellationToken = default)
        {
            if (!_isConnected)
            {
                LastErrorCode = InovanceErrorCode.NotConnected;
                _logger?.LogWarning("PLC未连接，无法写入变量", EventIds.PlcNotConnected);
                return false;
            }

            try
            {
                if (info is PlcVarWriteInfo writeInfo)
                {
                    var result = await _client!.WriteVariableAsync(writeInfo.Name, writeInfo.Value, cancellationToken);
                    if (result)
                    {
                        LastErrorCode = InovanceErrorCode.ReadWriteSucceed;
                        _logger?.LogDebug($"成功写入变量: {writeInfo.Name} = {writeInfo.Value}", EventIds.PlcWriteSuccess);
                        return true;
                    }
                    else
                    {
                        LastErrorCode = InovanceErrorCode.ReadWriteFail;
                        _logger?.LogWarning($"写入变量失败: {writeInfo.Name} = {writeInfo.Value}", EventIds.PlcWriteFailed);
                        return false;
                    }
                }
                else
                {
                    LastErrorCode = InovanceErrorCode.InvalidParameter;
                    _logger?.LogError("写入变量参数类型错误", EventIds.PlcInvalidParameter);
                    return false;
                }
            }
            catch (OperationCanceledException)
            {
                LastErrorCode = InovanceErrorCode.OperationTimeout;
                _logger?.LogWarning("写入变量操作被取消", EventIds.PlcOperationCancelled);
                return false;
            }
            catch (Exception ex)
            {
                LastErrorCode = InovanceErrorCode.CommunicationException;
                _logger?.LogError(ex, $"写入变量异常: {ex.Message}", EventIds.PlcWriteFailed);
                return false;
            }
        }

        /// <summary>
        /// 批量读取变量
        /// </summary>
        public async Task<IDictionary<string, object?>> ReadVariablesBatchAsync<TReadInfo>(IEnumerable<TReadInfo> infos, CancellationToken cancellationToken = default)
        {
            var results = new Dictionary<string, object?>();

            if (!_isConnected)
            {
                LastErrorCode = InovanceErrorCode.NotConnected;
                _logger?.LogWarning("PLC未连接，无法批量读取变量", EventIds.PlcNotConnected);
                return results;
            }

            try
            {
                foreach (var info in infos)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    if (info is PlcVarReadInfo readInfo)
                    {
                        var result = await ReadVariableAsync(readInfo, cancellationToken);
                        results[readInfo.Name] = result;
                    }
                }

                _logger?.LogDebug($"批量读取变量完成，共 {results.Count} 个变量", EventIds.PlcBatchReadComplete);
                return results;
            }
            catch (Exception ex)
            {
                LastErrorCode = InovanceErrorCode.CommunicationException;
                _logger?.LogError(ex, $"批量读取变量异常: {ex.Message}", EventIds.PlcBatchReadFailed);
                return results;
            }
        }

        /// <summary>
        /// 批量写入变量
        /// </summary>
        public async Task<IDictionary<string, bool>> WriteVariablesBatchAsync<TWriteInfo>(IEnumerable<TWriteInfo> infos, CancellationToken cancellationToken = default)
        {
            var results = new Dictionary<string, bool>();

            if (!_isConnected)
            {
                LastErrorCode = InovanceErrorCode.NotConnected;
                _logger?.LogWarning("PLC未连接，无法批量写入变量", EventIds.PlcNotConnected);
                return results;
            }

            try
            {
                foreach (var info in infos)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    if (info is PlcVarWriteInfo writeInfo)
                    {
                        var result = await WriteVariableAsync(writeInfo, cancellationToken);
                        results[writeInfo.Name] = result;
                    }
                }

                _logger?.LogDebug($"批量写入变量完成，共 {results.Count} 个变量", EventIds.PlcBatchWriteComplete);
                return results;
            }
            catch (Exception ex)
            {
                LastErrorCode = InovanceErrorCode.CommunicationException;
                _logger?.LogError(ex, $"批量写入变量异常: {ex.Message}", EventIds.PlcBatchWriteFailed);
                return results;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化客户端
        /// </summary>
        private void InitializeClient()
        {
            lock (LockObject)
            {
                if (_client == null)
                {
                    _client = new InovancePlcClient(_logger);
                }
            }
        }

        /// <summary>
        /// 内部连接实现
        /// </summary>
        private void ConnectInternal()
        {
            lock (LockObject)
            {
                // 防止频繁连接尝试
                if (_isConnecting)
                {
                    _logger?.LogWarning("PLC正在连接中，请稍候", EventIds.PlcConnectInProgress);
                    return;
                }

                if (DateTime.Now - _lastConnectionAttempt < MinConnectionInterval)
                {
                    _logger?.LogWarning("连接尝试过于频繁，请稍候", EventIds.PlcConnectTooFrequent);
                    return;
                }

                try
                {
                    _isConnecting = true;
                    _lastConnectionAttempt = DateTime.Now;

                    if (_connectionInfo == null)
                    {
                        throw new InvalidOperationException("连接信息未设置");
                    }

                    _logger?.LogInformation($"正在连接到PLC: {_connectionInfo.Address}:{_connectionInfo.Port}", 
                        EventIds.PlcConnectAttempt);

                    if (_client?.Connect(_connectionInfo.Address, CancellationToken.None) == true)
                    {
                        _isConnected = true;
                        LastErrorCode = InovanceErrorCode.ReadWriteSucceed;
                        _logger?.LogInformation($"PLC连接成功: {_connectionInfo.Address}:{_connectionInfo.Port}", 
                            EventIds.PlcConnectSuccess);
                    }
                    else
                    {
                        _isConnected = false;
                        LastErrorCode = InovanceErrorCode.NotConnected;
                        _logger?.LogError($"PLC连接失败: {_connectionInfo.Address}:{_connectionInfo.Port}", 
                            EventIds.PlcConnectFailed);
                    }
                }
                catch (Exception ex)
                {
                    _isConnected = false;
                    LastErrorCode = InovanceErrorCode.CommunicationException;
                    _logger?.LogError(ex, $"PLC连接异常: {ex.Message}", EventIds.PlcConnectFailed);
                }
                finally
                {
                    _isConnecting = false;
                }
            }
        }

        #endregion
    }
}
