namespace WaferAligner.Communication.Inovance
{
    /// <summary>
    /// 汇川PLC变量变化事件参数
    /// </summary>
    public class InovanceVariableChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="handler">处理器句柄</param>
        /// <param name="value">变量值</param>
        public InovanceVariableChangedEventArgs(string name, uint handler, object? value)
        {
            Name = name ?? string.Empty;
            Handle = handler;
            Value = value;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 变量值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 变量名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 处理器句柄
        /// </summary>
        public uint Handle { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 变量类型
        /// </summary>
        public Type? ValueType { get; set; }

        public override string ToString()
        {
            return $"{Name} = {Value} (Handle: {Handle}, Time: {Timestamp:HH:mm:ss.fff})";
        }
    }
}
