<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
    <AssemblyName>WaferAligner.Communication.Abstractions</AssemblyName>
    <RootNamespace>WaferAligner.Communication.Abstractions</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Communication.Abstractions</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner通信抽象层，定义PLC和串口通信的基础接口</Description>
    <PackageTags>WaferAligner;Communication;PLC;Abstractions;Interface</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Beckhoff.TwinCAT.Ads" Version="6.0.216" />
    <PackageReference Include="Beckhoff.TwinCAT.Ads.Reactive" Version="6.0.216" />
  </ItemGroup>

</Project>
