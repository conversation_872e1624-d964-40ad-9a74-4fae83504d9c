using System;
using System.Collections.Generic;

namespace WaferAligner.Communication.Abstractions
{
    /// <summary>
    /// PLC变量读取信息
    /// </summary>
    public class PlcVarReadInfo
    {
        /// <summary>
        /// 变量名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 变量类型
        /// </summary>
        public Type? Type { get; set; }

        /// <summary>
        /// 变量地址（可选，用于某些PLC类型）
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 读取超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 构造函数
        /// </summary>
        public PlcVarReadInfo() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="type">变量类型</param>
        public PlcVarReadInfo(string name, Type? type = null)
        {
            Name = name;
            Type = type;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="address">变量地址</param>
        /// <param name="type">变量类型</param>
        public PlcVarReadInfo(string name, string address, Type? type = null)
        {
            Name = name;
            Address = address;
            Type = type;
        }

        public override string ToString()
        {
            return $"ReadInfo: {Name} ({Type?.Name ?? "Unknown"})";
        }
    }

    /// <summary>
    /// PLC变量写入信息
    /// </summary>
    public class PlcVarWriteInfo
    {
        /// <summary>
        /// 变量名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 变量值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 变量地址（可选，用于某些PLC类型）
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 写入超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 是否验证写入结果
        /// </summary>
        public bool VerifyWrite { get; set; } = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public PlcVarWriteInfo() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="value">变量值</param>
        public PlcVarWriteInfo(string name, object? value)
        {
            Name = name;
            Value = value;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="address">变量地址</param>
        /// <param name="value">变量值</param>
        public PlcVarWriteInfo(string name, string address, object? value)
        {
            Name = name;
            Address = address;
            Value = value;
        }

        public override string ToString()
        {
            return $"WriteInfo: {Name} = {Value}";
        }
    }

    /// <summary>
    /// PLC变量批量操作信息
    /// </summary>
    public class PlcVarBatchInfo
    {
        /// <summary>
        /// 读取变量列表
        /// </summary>
        public List<PlcVarReadInfo> ReadVariables { get; set; } = new List<PlcVarReadInfo>();

        /// <summary>
        /// 写入变量列表
        /// </summary>
        public List<PlcVarWriteInfo> WriteVariables { get; set; } = new List<PlcVarWriteInfo>();

        /// <summary>
        /// 批量操作超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 10000;

        /// <summary>
        /// 是否使用事务（如果PLC支持）
        /// </summary>
        public bool UseTransaction { get; set; } = false;

        /// <summary>
        /// 添加读取变量
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="type">变量类型</param>
        public void AddReadVariable(string name, Type? type = null)
        {
            ReadVariables.Add(new PlcVarReadInfo(name, type));
        }

        /// <summary>
        /// 添加写入变量
        /// </summary>
        /// <param name="name">变量名称</param>
        /// <param name="value">变量值</param>
        public void AddWriteVariable(string name, object? value)
        {
            WriteVariables.Add(new PlcVarWriteInfo(name, value));
        }

        public override string ToString()
        {
            return $"BatchInfo: {ReadVariables.Count} reads, {WriteVariables.Count} writes";
        }
    }

    /// <summary>
    /// PLC连接信息
    /// </summary>
    public class PlcConnectionInfo
    {
        /// <summary>
        /// 连接类型
        /// </summary>
        public PlcConnectionType ConnectionType { get; set; } = PlcConnectionType.Ethernet;

        /// <summary>
        /// IP地址或连接路径
        /// </summary>
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; } = 502;

        /// <summary>
        /// 网络ID（用于某些PLC类型）
        /// </summary>
        public int NetId { get; set; } = 0;

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 重连间隔（毫秒）
        /// </summary>
        public int ReconnectIntervalMs { get; set; } = 3000;

        /// <summary>
        /// 最大重连次数
        /// </summary>
        public int MaxReconnectAttempts { get; set; } = 3;

        public override string ToString()
        {
            return $"PlcConnection: {ConnectionType} - {Address}:{Port}";
        }
    }

    /// <summary>
    /// PLC连接类型
    /// </summary>
    public enum PlcConnectionType
    {
        /// <summary>
        /// 以太网连接
        /// </summary>
        Ethernet,

        /// <summary>
        /// 串口连接
        /// </summary>
        Serial,

        /// <summary>
        /// ADS连接（倍福）
        /// </summary>
        Ads,

        /// <summary>
        /// ModbusTCP连接
        /// </summary>
        ModbusTcp,

        /// <summary>
        /// ModbusRTU连接
        /// </summary>
        ModbusRtu
    }
}
