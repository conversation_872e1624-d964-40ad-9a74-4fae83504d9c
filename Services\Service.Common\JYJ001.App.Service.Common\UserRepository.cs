using System.Threading.Tasks;
using Aya.DataBase;
using Aya.DataModel;
using JYJ001.App.Service.Common.Interface;

namespace JYJ001.App.Service.Common
{
    public class UserRepository : BaseRepository<User, UserDbContext>, IUserRepository
    {

        public UserRepository(IConfig config):base(new UserDbContext(config.GetSystemValue("PostgresqlConnectString").ToString()))
        {
        }
    }

    public class UserUnitOfWork : IUnitOfWork<UserDbContext>
    {
        private UserDbContext context;
        public UserUnitOfWork(UserDbContext context)
        {
            this.context = context;
        }
        public UserDbContext GetDbContext()
        {
            return this.context;
        }

        public async Task<int> SaveChangeAsync()
        {
            return await context.SaveChangesAsync();
        }
    }
}

