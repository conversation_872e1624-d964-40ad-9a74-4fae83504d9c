﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <AssemblyName>WaferAligner.Services.Abstractions</AssemblyName>
    <RootNamespace>WaferAligner.Services.Abstractions</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Services.Abstractions</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner服务抽象层，定义核心服务接口</Description>
    <PackageTags>WaferAligner;Services;Abstractions;Interface</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="IAccountService.cs" />
    <Compile Remove="IBaseRespository.cs" />
    <Compile Remove="IUserRepository.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.11" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Business\JYJ001.App.Business\JYJ001.App.Business.csproj" />
  </ItemGroup>
</Project>
