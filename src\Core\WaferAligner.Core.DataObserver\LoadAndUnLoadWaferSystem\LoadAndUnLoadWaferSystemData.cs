﻿using System;
using System.Collections.Generic;
using System.Security.Policy;


namespace JYJ001.App.DataObserver.LoadAndUnLoadWaferSystem
{
    public class LoadAndUnLoadWaferSystemData : AbstractSystemData
    {
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>
        {
            ["Axis.Axis1_RealVelo"] = typeof(float),
            ["Axis.Axis1_RealDistance"] = typeof(double),

            ["Axis.Axis1_Ready"] = typeof(bool),
            ["Axis.Axis1_Powered"] = typeof(bool),

            ["Axis.Axis2_RealVelo"] = typeof(float),
            ["Axis.Axis2_RealDistance"] = typeof(double),

            ["Axis.Axis2_Ready"] = typeof(bool),
            ["Axis.Axis2_Powered"] = typeof(bool),

            ["Axis.RobotResetFinished"] = typeof(bool),
            ["Axis.RobotPutFinished"] = typeof(bool),
            ["Axis.RobotGetFinished"] = typeof(bool),

            ["Axis.Axis1_MoveABSDone"] = typeof(bool),
            ["Axis.Axis2_MoveABSDone"] = typeof(bool),


        };
        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }

        public LoadAndUnLoadWaferSystemData()
        {
            DataUpdate = new Dictionary<string, Action<object>>()
            {
                ["Axis.Axis1_RealVelo"] = v => { Axis1_RealVelo = (float)v; },
                ["Axis.Axis1_RealDistance"] = v => { Axis1_RealDistance = (double)v; },
                ["Axis.Axis1_Powered"] = v => { Axis1_Powered = (bool)v; },

                ["Axis.Axis2_RealVelo"] = v => { Axis2_RealVelo = (float)v; },
                ["Axis.Axis2_RealDistance"] = v => { Axis2_RealDistance = (double)v; },
                ["Axis.Axis2_Powered"] = v => { Axis2_Powered = (bool)v; },

                ["Axis.RobotResetFinished"] = v => { RobotResetFinished = (bool)v; },
                ["Axis.RobotPutFinished"] = v => { RobotPutFinished = (bool)v; },
                ["Axis.RobotGetFinished"] = v => { RobotGetFinished = (bool)v; },

                ["Axis.Axis1_MoveABSDone"] = v => { Axis1_MoveABSDone = (bool)v; },
                ["Axis.Axis2_MoveABSDone"] = v => { Axis2_MoveABSDone = (bool)v; },
            };
            DataCheck = new Dictionary<string, Func<object>>
            {
                ["Axis1_Powered"] = () => Axis1_Powered,
                ["Axis2_Powered"] = () => Axis2_Powered,
                ["RobotResetFinished"] = () => RobotResetFinished,
                ["RobotPutFinished"] = () => RobotPutFinished,
                ["RobotGetFinished"] = () => RobotGetFinished,
                ["Axis1_MoveABSDone"] = () => Axis1_MoveABSDone,
                ["Axis2_MoveABSDone"] = () => Axis2_MoveABSDone,
            };
        }

        //当前速度
        public float Axis1_RealVelo;
        //当前位置
        public double Axis1_RealDistance;
        //使能
        public bool Axis1_Powered;


        public float Axis2_RealVelo;
        public double Axis2_RealDistance;
        public bool Axis2_Powered;

        public bool RobotResetFinished;
        public bool RobotPutFinished;
        public bool RobotGetFinished;

        public bool Axis1_MoveABSDone;
        public bool Axis2_MoveABSDone;


        public override void DataInitial() { }
    }
}
