﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner</id>
    <version>1.0.0</version>
    <authors>WaferAligner</authors>
    <description>Package Description</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="PLC.Base" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="PLC.Inovance" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="CommunityToolkit.Mvvm" version="8.2.2" exclude="Build,Analyzers" />
        <dependency id="SunnyUI" version="3.5.0" exclude="Build,Analyzers" />
        <dependency id="SunnyUI.Common" version="3.5.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net6.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WindowsForms" />
      </group>
    </frameworkReferences>
    <contentFiles>
      <files include="any/net6.0-windows7.0/gerber.ico" buildAction="Content" />
    </contentFiles>
  </metadata>
  <files>
    <file src="D:\对准设备\对准设备软件\WaferAligner\Debug\net6.0-windows7.0\WaferAligner.runtimeconfig.json" target="lib\net6.0-windows7.0\WaferAligner.runtimeconfig.json" />
    <file src="D:\对准设备\对准设备软件\WaferAligner\Debug\net6.0-windows7.0\WaferAligner.dll" target="lib\net6.0-windows7.0\WaferAligner.dll" />
    <file src="D:\对准设备\仪综所\WaferAligner-20250209 -仪综所\WaferAligner\gerber.ico" target="content\gerber.ico" />
    <file src="D:\对准设备\仪综所\WaferAligner-20250209 -仪综所\WaferAligner\gerber.ico" target="contentFiles\any\net6.0-windows7.0\gerber.ico" />
  </files>
</package>