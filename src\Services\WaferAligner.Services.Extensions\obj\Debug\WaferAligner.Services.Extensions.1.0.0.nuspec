﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner.Services.Extensions</id>
    <version>1.0.0</version>
    <authors>WaferAligner Team</authors>
    <description>WaferAligner服务扩展方法</description>
    <tags>WaferAligner Services Extensions Utilities</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="WaferAligner.Services.Abstractions" version="1.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\WaferAligner-0717-3.8.12-nullimple-75\src\Services\WaferAligner.Services.Extensions\bin\Debug\net6.0-windows\WaferAligner.Services.Extensions.dll" target="lib\net6.0-windows7.0\WaferAligner.Services.Extensions.dll" />
  </files>
</package>