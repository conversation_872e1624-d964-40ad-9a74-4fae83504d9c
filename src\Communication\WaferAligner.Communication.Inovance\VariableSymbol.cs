using System.Runtime.InteropServices;

namespace WaferAligner.Communication.Inovance
{
    /// <summary>
    /// PLC变量符号类
    /// 用于管理PLC变量的元数据和值
    /// </summary>
    public class VariableSymbol
    {
        #region 属性

        /// <summary>
        /// 数据类型
        /// </summary>
        public Type DataType { get; private set; }

        /// <summary>
        /// 变量名称
        /// </summary>
        public string Name { get; private set; }

        /// <summary>
        /// 注册数量
        /// </summary>
        public uint RegistryNumber { get; private set; }

        /// <summary>
        /// 处理器句柄
        /// </summary>
        public readonly uint Handler;

        /// <summary>
        /// 变量地址
        /// </summary>
        public int Address { get; private set; }

        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public int Size { get; private set; }

        #endregion

        #region 私有字段

        private byte[] value;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="handler">处理器句柄</param>
        /// <param name="symbol">符号节点</param>
        public VariableSymbol(uint handler, SymbolNode symbol)
        {
            Handler = handler;
            Address = symbol.Address?.Value ?? 0;
            Name = symbol.Name ?? string.Empty;
            DataType = symbol.Type ?? typeof(UInt16);
            
            // 根据数据类型确定大小
            Size = GetSizeFromType(DataType);
            value = new byte[Size];
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 增加通知注册数量
        /// </summary>
        public void IncreaseNotificationRegistry() => RegistryNumber += 1;

        /// <summary>
        /// 减少通知注册数量
        /// </summary>
        public void DecreaseNotificationRegistry() 
        {
            if (RegistryNumber > 0)
                RegistryNumber -= 1;
        }

        /// <summary>
        /// 设置变量值
        /// </summary>
        /// <param name="source">源数据</param>
        public void SetValue(Span<byte> source)
        {
            if (source.Length >= Size)
            {
                value = source.Slice(0, Size).ToArray();
            }
            else
            {
                // 如果源数据不够，用零填充
                value = new byte[Size];
                source.CopyTo(value);
            }
        }

        /// <summary>
        /// 获取变量值
        /// </summary>
        /// <returns>转换后的变量值</returns>
        public object? GetValue()
        {
            return CastValue();
        }

        /// <summary>
        /// 设置变量值（从对象）
        /// </summary>
        /// <param name="newValue">新值</param>
        public void SetValue(object? newValue)
        {
            if (newValue == null)
            {
                value = new byte[Size];
                return;
            }

            try
            {
                value = ConvertToBytes(newValue, DataType);
            }
            catch (Exception)
            {
                // 转换失败时保持原值
                value = new byte[Size];
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 根据数据类型获取大小
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <returns>字节大小</returns>
        private static int GetSizeFromType(Type type)
        {
            return type.Name.ToLower() switch
            {
                "boolean" => 1,
                "byte" => 1,
                "sbyte" => 1,
                "int16" => 2,
                "uint16" => 2,
                "int32" => 4,
                "uint32" => 4,
                "int64" => 8,
                "uint64" => 8,
                "single" => 4,
                "double" => 8,
                _ => 2 // 默认为2字节
            };
        }

        /// <summary>
        /// 将字节数组转换为对应的数据类型
        /// </summary>
        /// <returns>转换后的值</returns>
        private object? CastValue()
        {
            if (value == null || value.Length == 0)
                return null;

            try
            {
                return DataType.Name.ToLower() switch
                {
                    "boolean" => BitConverter.ToBoolean(value, 0),
                    "byte" => value[0],
                    "sbyte" => (sbyte)value[0],
                    "uint16" => BitConverter.ToUInt16(value, 0),
                    "int16" => BitConverter.ToInt16(value, 0),
                    "int32" => BitConverter.ToInt32(value, 0),
                    "uint32" => BitConverter.ToUInt32(value, 0),
                    "int64" => BitConverter.ToInt64(value, 0),
                    "uint64" => BitConverter.ToUInt64(value, 0),
                    "single" => BitConverter.ToSingle(value, 0),
                    "double" => BitConverter.ToDouble(value, 0),
                    _ => BitConverter.ToUInt16(value, 0) // 默认为UInt16
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 将对象转换为字节数组
        /// </summary>
        /// <param name="obj">要转换的对象</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>字节数组</returns>
        private static byte[] ConvertToBytes(object obj, Type targetType)
        {
            return targetType.Name.ToLower() switch
            {
                "boolean" => BitConverter.GetBytes(Convert.ToBoolean(obj)),
                "byte" => new[] { Convert.ToByte(obj) },
                "sbyte" => new[] { (byte)Convert.ToSByte(obj) },
                "uint16" => BitConverter.GetBytes(Convert.ToUInt16(obj)),
                "int16" => BitConverter.GetBytes(Convert.ToInt16(obj)),
                "int32" => BitConverter.GetBytes(Convert.ToInt32(obj)),
                "uint32" => BitConverter.GetBytes(Convert.ToUInt32(obj)),
                "int64" => BitConverter.GetBytes(Convert.ToInt64(obj)),
                "uint64" => BitConverter.GetBytes(Convert.ToUInt64(obj)),
                "single" => BitConverter.GetBytes(Convert.ToSingle(obj)),
                "double" => BitConverter.GetBytes(Convert.ToDouble(obj)),
                _ => BitConverter.GetBytes(Convert.ToUInt16(obj)) // 默认为UInt16
            };
        }

        #endregion

        #region 重写方法

        public override string ToString()
        {
            return $"{Name} ({DataType.Name}) @ {Address} = {GetValue()}";
        }

        public override bool Equals(object? obj)
        {
            if (obj is VariableSymbol other)
            {
                return Handler == other.Handler && Name == other.Name;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Handler, Name);
        }

        #endregion
    }
}
