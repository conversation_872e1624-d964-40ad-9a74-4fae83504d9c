{"runtimeOptions": {"tfm": "net6.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "6.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "6.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configProperties": {"System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}