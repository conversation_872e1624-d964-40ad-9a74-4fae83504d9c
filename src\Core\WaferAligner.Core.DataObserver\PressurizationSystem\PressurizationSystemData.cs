﻿using System;
using System.Collections.Generic;

namespace JYJ001.App.DataObserver.PressurizationSystem
{
    public class PressurizationSystemData : AbstractSystemData
    {
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>
        {
            //["HydriacalGasPressureCylinder.ActualPressureValue"] = typeof(float),
            //["HydriacalGasPressureCylinder.PressurizeCylinderStatus"] = typeof(bool),
            //["HydriacalGasPressureCylinder.PositionMonitor"]=typeof(float)
            ["GTPressControl.Position"] = typeof(Int32),
            ["GTPressControl.Pressure"] = typeof(Int32),
            ["GTPressControl.GTMotorStopped"] = typeof(bool),
            ["GTPressControl.RealTimeMovingStatus"] = typeof(UInt16),
            ["GTPressControl.ConfigFinished"] = typeof(bool),
        };
        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }

        public PressurizationSystemData()
        {
            DataUpdate = new Dictionary<string, Action<object>>()
            {
                ["GTPressControl.Pressure"] = v => Pressure = (Int32)v,
                ["GTPressControl.Position"] = v => Position = (Int32)v,
                ["GTPressControl.GTMotorStopped"] = v => GTMotorStopped = (bool)v,
                ["GTPressControl.RealTimeMovingStatus"] = v => RealTimeMovingStatus = (UInt16)v,
                ["GTPressControl.ConfigFinished"] = v => ConfigFinished = (bool)v,

                //["HydriacalGasPressureCylinder.PressurizeCylinderStatus"] = 
                //    v => { 
                //        Released = !(bool)v; 
                //    },

            };

            DataCheck = new Dictionary<string, Func<object>>
            {
                ["Pressure"] = () => Pressure,
                ["Position"] = () => Position,
                ["GTMotorStopped"] = () => GTMotorStopped,
                ["RealTimeMovingStatus"] = () => RealTimeMovingStatus,
                ["ConfigFinished"] = () => ConfigFinished,
            };
        }

        private Int32 Pressure;
        private bool GTMotorStopped;
        private Int32 Position;
        private UInt16 RealTimeMovingStatus;
        private bool ConfigFinished;

        public override void DataInitial()
        {

        }
    }
}
