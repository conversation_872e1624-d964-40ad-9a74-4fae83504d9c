//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("WaferAligner Team")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("WaferAligner核心业务逻辑，包含配方管理、用户认证、日志等核心功能")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("WaferAligner.Core.Business")]
[assembly: System.Reflection.AssemblyTitleAttribute("WaferAligner.Core.Business")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// 由 MSBuild WriteCodeFragment 类生成。

