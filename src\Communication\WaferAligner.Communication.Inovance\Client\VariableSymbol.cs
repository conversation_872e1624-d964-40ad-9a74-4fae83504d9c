using System.Runtime.InteropServices;
namespace WaferAligner.Communication.Inovance.Client
{
    public class VariableSymbol
    {
        public VariableSymbol(uint handler, SymbolNode symbol)
        {
            Handler = handler;
            Address = symbol.Address.Value;
            Name = symbol.Name;
            Size = symbol.Type.Name.ToLower() switch
            {
                "boolean" => 1,
                "byte" => 1,
                "sbyte" => 1,
                "int16" => 2,
                "uint16" => 2,
                "int32" => 4,
                "uint32" => 4,
                "single" => 4,
                "double" => 8,
                _ => 2
            };
            DataType = symbol.Type;
            value = new byte[Size];
            //value_pointer = Marshal.AllocHGlobal(Size);
        }
        public Type DataType;
        public string Name;
        public uint RegistryNumber;
        public readonly uint Handler;
        public int Address;
        //IntPtr value_pointer;
        byte[] value;
        public int Size;
        public void IncreaseNotificationRegistry() => this.RegistryNumber += 1;
        public void DecreaseNotificationRegistry() => this.RegistryNumber -= 1;
        public void SetValue(Span<byte> source)
        {
            value = source.ToArray();
            //Marshal.Copy(source.ToArray(), 0, (IntPtr)value_pointer, source.Length);
        }

        //public IntPtr GetValuePointer()
        //{
        //    return value_pointer;
        //}
        public object GetValue()
        {
            return CastValue();
        }
        object CastValue()
        {
            switch (DataType.Name.ToLower())
            {
                case "boolean":
                    return BitConverter.ToBoolean(value);
                case "byte" or "sbyte":
                    return value[0];
                case "uint16":
                    return BitConverter.ToUInt16(value);
                case "int16":
                    return BitConverter.ToInt16(value);
                case "int32":
                    return BitConverter.ToInt32(value);
                case "uint32":
                    return BitConverter.ToUInt32(value);
                case "int64":
                    return BitConverter.ToInt64(value);
                case "single":
                    return BitConverter.ToSingle(value);
                case "double":
                    return BitConverter.ToDouble(value);
                default:
                    return null;
            };
        }
    }
}
