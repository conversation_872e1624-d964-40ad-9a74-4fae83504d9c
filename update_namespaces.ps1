# PowerShell脚本：批量更新命名空间

# 更新汇川PLC项目中的命名空间
$inovanceFiles = Get-ChildItem -Path "src\Communication\WaferAligner.Communication.Inovance" -Filter "*.cs" -Recurse

foreach ($file in $inovanceFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # 替换命名空间
    $content = $content -replace "namespace PLC\.Inovance", "namespace WaferAligner.Communication.Inovance"
    $content = $content -replace "using PLC\.Inovance", "using WaferAligner.Communication.Inovance"
    $content = $content -replace "PLC\.Inovance\.Client", "WaferAligner.Communication.Inovance.Client"
    
    # 保存文件
    Set-Content -Path $file.FullName -Value $content -NoNewline
    Write-Host "Updated: $($file.FullName)"
}

Write-Host "命名空间更新完成！"
