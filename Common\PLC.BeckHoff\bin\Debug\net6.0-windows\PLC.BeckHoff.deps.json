{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"PLC.BeckHoff/1.0.0": {"dependencies": {"Beckhoff.TwinCAT.Ads": "6.0.216", "Beckhoff.TwinCAT.Ads.Reactive": "6.0.216", "JYJ001.App.Service.Common.Extension": "1.0.0", "JYJ001.App.Service.Common.Interface": "1.0.0", "PLC.Base": "1.0.0"}, "runtime": {"PLC.BeckHoff.dll": {}}}, "Beckhoff.TwinCAT.Ads/6.0.216": {"dependencies": {"Beckhoff.TwinCAT.Ads.Server": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.ComponentModel.Composition": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Beckhoff.TwinCAT.Ads.Abstractions/6.0.216": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Configuration.ConfigurationManager": "6.0.0", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Beckhoff.TwinCAT.Ads.Reactive/6.0.216": {"dependencies": {"Beckhoff.TwinCAT.Ads.Abstractions": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Reactive": "5.0.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.Reactive.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Beckhoff.TwinCAT.Ads.Server/6.0.216": {"dependencies": {"Beckhoff.TwinCAT.Ads.Abstractions": "6.0.216", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Reactive": "5.0.0"}, "runtime": {"lib/net6.0/TwinCAT.Ads.Server.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.216.0"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.EntityFrameworkCore/6.0.11": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.11", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.11", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.1122.51302"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.11": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.1122.51302"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.11": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.11", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.722.31501"}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.2.27524"}}}, "Npgsql/6.0.7": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.7.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.11", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.11", "Microsoft.EntityFrameworkCore.Relational": "6.0.7", "Npgsql": "6.0.7"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "6.0.7.0", "fileVersion": "6.0.7.0"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.ComponentModel.Composition/6.0.0": {"runtime": {"lib/net6.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.424.16909"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.1"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "JYJ001.App.Business/1.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Reactive": "5.0.0"}, "runtime": {"JYJ001.App.Business.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "JYJ001.App.DataModel/1.0.0": {"dependencies": {"JYJ001.App.Business": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Newtonsoft.Json": "13.0.2"}, "runtime": {"JYJ001.App.DataModel.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "JYJ001.APP.ORM/1.0.0": {"dependencies": {"JYJ001.App.Business": "1.0.0", "JYJ001.App.DataModel": "1.0.0", "Microsoft.EntityFrameworkCore": "6.0.11", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.7"}, "runtime": {"JYJ001.APP.ORM.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "JYJ001.App.Service.Common.Extension/1.0.0": {"dependencies": {"JYJ001.App.Service.Common.Interface": "1.0.0"}, "runtime": {"JYJ001.App.Service.Common.Extension.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "JYJ001.App.Service.Common.Interface/1.0.0": {"dependencies": {"JYJ001.APP.ORM": "1.0.0", "JYJ001.App.DataModel": "1.0.0", "Microsoft.EntityFrameworkCore": "6.0.11"}, "runtime": {"JYJ001.App.Service.Common.Interface.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "PLC.Base/1.0.0": {"dependencies": {"Beckhoff.TwinCAT.Ads": "6.0.216", "Beckhoff.TwinCAT.Ads.Reactive": "6.0.216"}, "runtime": {"PLC.Base.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"PLC.BeckHoff/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Beckhoff.TwinCAT.Ads/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-V+LccEj1k31u8pD6ETxrht/oK2aMhPRJ7SZnNYLkUucd2q3A2D+KcUYWTm2PGCrCkosohnf/VGbGAfl5EudW0g==", "path": "beckhoff.twincat.ads/6.0.216", "hashPath": "beckhoff.twincat.ads.6.0.216.nupkg.sha512"}, "Beckhoff.TwinCAT.Ads.Abstractions/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-16yaujxvLpWsNk+PUny75sUfH4oN8FUvszwGqEZ4BeScVjgGeMjCeOzIHrP8PN+tLzAxfiR5TN/JSKfhNmaICw==", "path": "beckhoff.twincat.ads.abstractions/6.0.216", "hashPath": "beckhoff.twincat.ads.abstractions.6.0.216.nupkg.sha512"}, "Beckhoff.TwinCAT.Ads.Reactive/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-Nf7HUg4+x4B12u2wM0z8jNzR9U9JM0NiiQIu3n/EyU4sUN2pr4J+bPaBKL9JYn4agV4nUT0RIf4jdMXTtYoGVg==", "path": "beckhoff.twincat.ads.reactive/6.0.216", "hashPath": "beckhoff.twincat.ads.reactive.6.0.216.nupkg.sha512"}, "Beckhoff.TwinCAT.Ads.Server/6.0.216": {"type": "package", "serviceable": true, "sha512": "sha512-ZpNtECkCWfr4CQ7tuo7ps5uw9frs11wD9kzbsu0doHK5B+YrHEUcZh6uKpGAfCZyU2TBv2UlLtCBhQdC8WwD4w==", "path": "beckhoff.twincat.ads.server/6.0.216", "hashPath": "beckhoff.twincat.ads.server.6.0.216.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-eUsIZ52uBJFCr/OUL1EHp0BAwdkfHFVGMyXYrkGUjkSWtPd751wgFzgWBstxOQYzUEyKtz1/wC72S8Db0vPvsg==", "path": "microsoft.entityframeworkcore/6.0.11", "hashPath": "microsoft.entityframeworkcore.6.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-KJCJjFMZFGYy0G8a8ZUwAe9n/l6P+dP3i4fQJmR4jR0/EFnlfeNeWh8n6nRhP+9YmNz290twaIZSbRoiGU6S2A==", "path": "microsoft.entityframeworkcore.abstractions/6.0.11", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-xke0hphu+BSBwt6Kfv/XERe3s1G7BZjNUByyNj0oIZVD1KPaIhMQJBKHtblkCI04cMnO1Ac2NMEgO67rM+cP/w==", "path": "microsoft.entityframeworkcore.analyzers/6.0.11", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-0uo4fPDHutMbd9AJJEKl2q/2fYuFGA8tEBE2fQeQFaNDd+F/aUjaXc1FUD84J7Wcax8WP40rZo1E0u9A0yPPZw==", "path": "microsoft.entityframeworkcore.relational/6.0.7", "hashPath": "microsoft.entityframeworkcore.relational.6.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "Npgsql/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-HhD5q/VUJY++tCzc0eCrhtsxmUdP7NxNhUMOdYW6sqpC6NRlFLvUDf5JyRj0gOGkXe3Tn49toaisgvLqlzQ2JQ==", "path": "npgsql/6.0.7", "hashPath": "npgsql.6.0.7.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-kqwX2V9Znw/QjLDL6SyYEHiy9toMuqMTyCJ4ebQu65FpV8pElYKpOlBbxoyr6YNgqieXEMmpnE0TIoTKsVdeLw==", "path": "npgsql.entityframeworkcore.postgresql/6.0.7", "hashPath": "npgsql.entityframeworkcore.postgresql.6.0.7.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.ComponentModel.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-60Qv+F7oxomOjJeTDA5Z4iCyFbQ0B/2Mi5HT+13pxxq0lVnu2ipbWMzFB+RWKr3wWKA8BSncXr9PH/fECwMX5Q==", "path": "system.componentmodel.composition/6.0.0", "hashPath": "system.componentmodel.composition.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "JYJ001.App.Business/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "JYJ001.App.DataModel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "JYJ001.APP.ORM/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "JYJ001.App.Service.Common.Extension/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "JYJ001.App.Service.Common.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PLC.Base/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}