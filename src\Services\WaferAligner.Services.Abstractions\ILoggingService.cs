﻿using JYJ001.App.Business;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Channels;

namespace WaferAligner.Services.Abstractions
{
    /// <summary>
    /// 增强的日志服务接口 - 重构版本
    /// 添加按模块日志级别控制、结构化日志、性能监控等新功能
    /// </summary>
    public interface ILoggingService
    {
        #region 现有接口 - 保持兼容性

        public IObservable<LogEntry<string>> LogFeed { get; }
        void Log(LogLevel logLevel, EventId eventId, string message);
        void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter);
        ChannelReader<LogEntry<string>> StreamLog();

        #endregion

        #region 新增功能接口

        /// <summary>
        /// 设置全局最低日志级别
        /// </summary>
        void SetMinimumLogLevel(LogLevel level);

        /// <summary>
        /// 获取全局最低日志级别
        /// </summary>
        LogLevel GetMinimumLogLevel();

        /// <summary>
        /// 设置指定模块的日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="level">日志级别</param>
        void SetModuleLogLevel(string moduleName, LogLevel level);

        /// <summary>
        /// 获取指定模块的日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>日志级别，如果未设置则返回全局级别</returns>
        LogLevel GetModuleLogLevel(string moduleName);

        /// <summary>
        /// 记录结构化日志
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="logLevel">日志级别</param>
        /// <param name="eventId">事件ID</param>
        /// <param name="message">消息</param>
        /// <param name="data">结构化数据</param>
        void LogStructured<T>(LogLevel logLevel, EventId eventId, string message, T data);

        /// <summary>
        /// 开始性能监控作用域
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="eventId">事件ID</param>
        /// <returns>性能监控作用域，Dispose时自动记录耗时</returns>
        IDisposable BeginPerformanceScope(string operationName, EventId eventId);

        /// <summary>
        /// 检查指定模块和级别是否启用日志记录
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="logLevel">日志级别</param>
        /// <returns>是否启用</returns>
        bool IsEnabled(string moduleName, LogLevel logLevel);

        #endregion
    }
}
