{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-20250509-V2.0-refactor1\\WaferAligner\\WaferAligner.Tests\\WaferAligner.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-20250509-V2.0-refactor1\\WaferAligner\\WaferAligner.Tests\\WaferAligner.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-20250509-V2.0-refactor1\\WaferAligner\\WaferAligner.Tests\\WaferAligner.Tests.csproj", "projectName": "WaferAligner.Tests", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-20250509-V2.0-refactor1\\WaferAligner\\WaferAligner.Tests\\WaferAligner.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-20250509-V2.0-refactor1\\WaferAligner\\WaferAligner.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MSTest": {"target": "Package", "version": "[3.6.4, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}