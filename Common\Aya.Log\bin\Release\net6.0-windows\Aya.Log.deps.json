{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Aya.Log/1.0.0": {"dependencies": {"JYJ001.App.Service.Common.Interface": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"Aya.Log.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.EntityFrameworkCore/6.0.11": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.11", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.11", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.1122.51302"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.11": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.1122.51302"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.11": {}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.424.16909"}}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.1"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "JYJ001.App.Business/1.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Reactive": "5.0.0"}, "runtime": {"JYJ001.App.Business.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "JYJ001.App.Service.Common.Interface/1.0.0": {"dependencies": {"JYJ001.App.Business": "1.0.0", "Microsoft.EntityFrameworkCore": "6.0.11"}, "runtime": {"JYJ001.App.Service.Common.Interface.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Aya.Log/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-eUsIZ52uBJFCr/OUL1EHp0BAwdkfHFVGMyXYrkGUjkSWtPd751wgFzgWBstxOQYzUEyKtz1/wC72S8Db0vPvsg==", "path": "microsoft.entityframeworkcore/6.0.11", "hashPath": "microsoft.entityframeworkcore.6.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-KJCJjFMZFGYy0G8a8ZUwAe9n/l6P+dP3i4fQJmR4jR0/EFnlfeNeWh8n6nRhP+9YmNz290twaIZSbRoiGU6S2A==", "path": "microsoft.entityframeworkcore.abstractions/6.0.11", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-xke0hphu+BSBwt6Kfv/XERe3s1G7BZjNUByyNj0oIZVD1KPaIhMQJBKHtblkCI04cMnO1Ac2NMEgO67rM+cP/w==", "path": "microsoft.entityframeworkcore.analyzers/6.0.11", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.11.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "JYJ001.App.Business/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "JYJ001.App.Service.Common.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}