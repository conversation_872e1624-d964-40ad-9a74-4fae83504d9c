﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner.Core.Business</id>
    <version>1.0.0</version>
    <authors>WaferAligner Team</authors>
    <description>WaferAligner核心业务逻辑，包含配方管理、用户认证、日志等核心功能</description>
    <tags>WaferAligner Core Business Recipe UserAuth Logging</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="7.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="7.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Reactive" version="5.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\WaferAligner-0717-3.8.12-nullimple-75\src\Core\WaferAligner.Core.Business\bin\Debug\net6.0\WaferAligner.Core.Business.dll" target="lib\net6.0\WaferAligner.Core.Business.dll" />
  </files>
</package>