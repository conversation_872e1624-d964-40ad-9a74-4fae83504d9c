﻿using System;
using System.Collections.Generic;

namespace JYJ001.App.DataObserver.Warning
{
    public class WarningSystemData : AbstractSystemData
    {
        public override Dictionary<string, Type> DataDic { get; protected set; } = new Dictionary<string, Type>
        {
            ["GVL.SafetySensor"] = typeof(bool),

            ["WarningSystem.WarningBeepEnable"] = typeof(bool),
            ["GVL.VacuumSystemErrorCode"] = typeof(UInt16),
            ["GVL.HeatSystemErrorCode"] = typeof(UInt16),
            ["GVL.PistonSystemErrorCode"] = typeof(UInt16),
            ["GVL.OtherSystemErrorCode"] = typeof(UInt16),
            ["GVL.Force_Stop"] = typeof(bool)

        };
        public override Dictionary<string, Action<object>> DataUpdate { get; protected set; }
        public override Dictionary<string, Func<object>> DataCheck { get; protected set; }

        public WarningSystemData()
        {
            DataInitial();
        }

        public override void DataInitial()
        {
            DataCheck = new Dictionary<string, Func<object>>
            {
                ["SafetySensor"] = () => SafetySensor,

                ["ForceStopKeyStatus"] = () => ForceStopKeyStatus,
                ["VacuumSystemErrorCode"] = () => VacuumSystemErrorCode,
                ["HeatSystemErrorCode"] = () => HeatSystemErrorCode,
                ["PistonSystemErrorCode"] = () => PistonSystemErrorCode,
                ["OtherSystemErrorCode"] = () => OtherSystemErrorCode,
                ["BeepStatus"] = () => BeepStatus,

            };
            DataUpdate = new Dictionary<string, Action<object>>
            {
                ["GVL.SafetySensor"] = v => { SafetySensor = Convert.ToBoolean(v); },

                ["GVL.VacuumSystemErrorCode"] = v => { VacuumSystemErrorCode = Convert.ToUInt16(v); },
                ["GVL.HeatSystemErrorCode"] = v => { VacuumSystemErrorCode = Convert.ToUInt16(v); },
                ["GVL.PistonSystemErrorCode"] = v => { VacuumSystemErrorCode = Convert.ToUInt16(v); },
                ["GVL.OtherSystemErrorCode"] = v => { VacuumSystemErrorCode = Convert.ToUInt16(v); },
                ["WarningSystem.WarningBeepEnable"] = v =>
                {
                    BeepStatus = Convert.ToBoolean(v);
                },

                ["GVL.Force_Stop"] = v => { ForceStopKeyStatus = Convert.ToBoolean(v); },




            };
        }

        private UInt16 VacuumSystemErrorCode;
        private UInt16 HeatSystemErrorCode;
        private UInt16 PistonSystemErrorCode;
        private UInt16 OtherSystemErrorCode;
        private bool ForceStopKeyStatus;
        private bool BeepStatus;
        private bool SafetySensor;
    }
}
