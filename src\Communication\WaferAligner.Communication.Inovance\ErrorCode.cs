namespace WaferAligner.Communication.Inovance
{
    /// <summary>
    /// 汇川PLC错误代码枚举
    /// </summary>
    public enum InovanceErrorCode
    {
        /// <summary>
        /// 读写失败
        /// </summary>
        ReadWriteFail = 0,

        /// <summary>
        /// 读写成功
        /// </summary>
        ReadWriteSucceed = 1,

        /// <summary>
        /// 未连接
        /// </summary>
        NotConnected = 2,

        /// <summary>
        /// 元件类型错误
        /// </summary>
        ElementTypeWrong = 3,

        /// <summary>
        /// 元件地址溢出
        /// </summary>
        ElementAddressOverflow = 4,

        /// <summary>
        /// 元件个数超限
        /// </summary>
        ElementCountOverflow = 5,

        /// <summary>
        /// 通讯异常
        /// </summary>
        CommunicationException = 6,

        /// <summary>
        /// 符号未找到
        /// </summary>
        SymbolNotFound = 7,

        /// <summary>
        /// 无错误
        /// </summary>
        None = 10,

        /// <summary>
        /// 连接超时
        /// </summary>
        ConnectionTimeout = 11,

        /// <summary>
        /// 操作超时
        /// </summary>
        OperationTimeout = 12,

        /// <summary>
        /// 参数无效
        /// </summary>
        InvalidParameter = 13,

        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown = 99
    }

    /// <summary>
    /// 汇川PLC错误代码扩展方法
    /// </summary>
    public static class InovanceErrorCodeExtensions
    {
        /// <summary>
        /// 获取错误代码的描述信息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>描述信息</returns>
        public static string GetDescription(this InovanceErrorCode errorCode)
        {
            return errorCode switch
            {
                InovanceErrorCode.ReadWriteFail => "读写操作失败",
                InovanceErrorCode.ReadWriteSucceed => "读写操作成功",
                InovanceErrorCode.NotConnected => "PLC未连接",
                InovanceErrorCode.ElementTypeWrong => "元件类型错误",
                InovanceErrorCode.ElementAddressOverflow => "元件地址溢出",
                InovanceErrorCode.ElementCountOverflow => "元件个数超出限制",
                InovanceErrorCode.CommunicationException => "通讯异常",
                InovanceErrorCode.SymbolNotFound => "符号未找到",
                InovanceErrorCode.None => "无错误",
                InovanceErrorCode.ConnectionTimeout => "连接超时",
                InovanceErrorCode.OperationTimeout => "操作超时",
                InovanceErrorCode.InvalidParameter => "参数无效",
                InovanceErrorCode.Unknown => "未知错误",
                _ => "未定义的错误代码"
            };
        }

        /// <summary>
        /// 检查是否为成功状态
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>是否成功</returns>
        public static bool IsSuccess(this InovanceErrorCode errorCode)
        {
            return errorCode == InovanceErrorCode.ReadWriteSucceed || errorCode == InovanceErrorCode.None;
        }

        /// <summary>
        /// 检查是否为连接相关错误
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <returns>是否为连接错误</returns>
        public static bool IsConnectionError(this InovanceErrorCode errorCode)
        {
            return errorCode == InovanceErrorCode.NotConnected ||
                   errorCode == InovanceErrorCode.ConnectionTimeout ||
                   errorCode == InovanceErrorCode.CommunicationException;
        }
    }
}
