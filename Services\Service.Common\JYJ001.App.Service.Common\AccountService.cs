﻿using Aya.DataModel;
using JYJ001.App.Business;
using JYJ001.App.Service.Common;
using JYJ001.App.Service.Common.Interface;
using JYJ001.App.Services.Common.Extension;
using JYJ001.App.Services.Common.Interfaces;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Timers;
using WaferAligner.Common;

namespace JYJ001.App.Services.Common
{
    public abstract class AbstractAccountService : IAccountService
    {
        public event IAccountService.LogEventHandler LogEvent = (s, e) => { };
        private UserInfo OperatorUserInfo { set; get; }

        public abstract void AddOrUpdateUser(User user);

        public UserInfo GetCurrentUser()
        {
            return OperatorUserInfo;
        }

        public abstract IRequestResult LogOut();

        public abstract void TransUserInfo();

        public abstract void RemoveUser(string name);

        public abstract Task<IRequestResult> LogInCheckAsync(string username, string password);
    }


    public class AccountService : IAccountService
    {
        public event IAccountService.LogEventHandler LogEvent = (s, e) => { };
        private UserInfo CurrentUserInfo { set; get; } = new();

        //public Dictionary<string, (string, UserAuth?)> userinfo = new Dictionary<string, (string, UserAuth?)>();

        private readonly ILoggingService _loggingService;
        private readonly IConfig _config;
        private readonly IUserRepository _userRepository;
        //private readonly IContainerProvider _containerProvider;
        private readonly Timer loginTimeOut = new Timer();
        public AccountService(ILoggingService loggingService,
                                IConfig config,
                                IUserRepository userRepository
                              //IContainerProvider containerProvider
                              )
        {
            _loggingService = loggingService;
            _config = config;
            _userRepository = userRepository;
            //_containerProvider = containerProvider;
            loginTimeOut.Interval = 10 * 1000;
            loginTimeOut.AutoReset = true;
            loginTimeOut.Elapsed += NoResponseTimeOut;
        }

        public async Task<IRequestResult> LogInCheckAsync(string name, string password)
        {
#if DEBUG
            CurrentUserInfo.Name = "Developper";
            CurrentUserInfo.Auth = UserAuth.Admin;
            LogEvent.Invoke(this, new LogEventArgs { Name = CurrentUserInfo.Name, NewAuth = UserAuth.Admin });
            _loggingService.LogInformation(@$"用户 : {CurrentUserInfo.Name} 登录成功, 权限 : {Enum.GetName<UserAuth>(UserAuth.Admin)}", EventIds.UserLoginSuccess);
            return new SuccessRequest { DataInfo = "Login Success!", Result = true };
#else
            var user = await _userRepository.GetEntity(u => u.Name == name);
            if (user != null)
            {
                if (user.Password == password)
                {
                    CurrentUserInfo.Name = name;
                    CurrentUserInfo.Auth = user.Auth;
                    _loggingService.LogInformation(@$"用户 : {CurrentUserInfo.Name} 登录成功, 权限 : {Enum.GetName(user.Auth)}!", EventIds.UserLoginSuccess);
                    return new SuccessRequest { DataInfo = "Login Success!", Result = true };
                }
                else
                {
                    return new FailedRequest { DataInfo = $"密码错误!" };
                }
            }
            else
            {
                return new FailedRequest { DataInfo = $"用户 : {name} 不存在!" };
            }
#endif
        }

        public IRequestResult LogOut()
        {
            _loggingService.LogInformation(@$"用户 : {CurrentUserInfo.Name} 登录退出!", EventIds.LoginCancelled);
            CurrentUserInfo.Name = null;
            CurrentUserInfo.Auth = null;
            LogEvent.Invoke(null, new LogEventArgs { NewAuth = null, Operation = LogOperation.LogOut });
            return new SuccessRequest { DataInfo = "", Result = true };
        }

        public async void AddOrUpdateUser(User user)
        {
            if (await _userRepository.IsExist(u => u.Name == user.Name))
            {
                _userRepository.Update(user);
            }
            else
            {
                await _userRepository.Insert(user);
            }
        }

        public UserInfo GetCurrentUser()
        {
            return CurrentUserInfo;
        }

        public void TransUserInfo()
        {
            LogEvent.Invoke(null, new LogEventArgs
            {
                Name = CurrentUserInfo.Name,
                NewAuth = CurrentUserInfo.Auth,
                Operation = LogOperation.LogIn
            });
        }

        public async void RemoveUser(string name)
        {
            try
            {
                if (string.IsNullOrEmpty(name))
                {
                    _loggingService?.LogWarning("删除用户失败：用户名为空", EventIds.UserDeleteFailed);
                    return;
                }

                var user = await _userRepository.GetEntity(u => u.Name == name);
                if (user == null)
                {
                    _loggingService?.LogWarning($"删除用户失败：用户 {name} 不存在", EventIds.UserDeleteFailed);
                    return;
                }

                // 防止删除当前登录用户
                if (name == CurrentUserInfo.Name)
                {
                    _loggingService?.LogWarning($"删除用户失败：不能删除当前登录用户 {name}", EventIds.UserDeleteFailed);
                    return;
                }

                await _userRepository.Delete(user);
                _loggingService?.LogInformation($"用户 {name} 删除成功", EventIds.UserDeleteSuccess);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"删除用户 {name} 时发生异常: {ex.Message}", EventIds.UserDeleteError);
            }
        }



        private void NoResponseTimeOut(object sender, EventArgs e)
        {

        }
    }
}
